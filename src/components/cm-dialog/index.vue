<template>
  <el-dialog modal-class="cm-dialog-modal" v-model="visible"
    class="cm-dialog-box"
    :show-close="false" v-bind="$attrs">
    <template #header="{ titleId, titleClass }">
      <div class="cm-dialog-box-header">
        <span :id="titleId"
          :class="titleClass">{{ $attrs.title }}</span>
        <button
          aria-label="关闭对话框"
          type="button"
          class="cm-dialog__headerbtn"
          @click="visible = false">
          <el-icon class="el-dialog__close">
            <CloseBold />
          </el-icon>
        </button>
      </div>
    </template>
    <template #default>
      <div class="cm-dialog-content">
        <slot></slot>
      </div>
    </template>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </el-dialog>
</template>
<script setup>
defineOptions({
  name: 'CmDialog',
})
const visible = defineModel({
  default: false,
})
</script>
<style lang="scss">
.cm-dialog-modal {
  .el-overlay-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cm-dialog-box {
    padding: 0;
    margin: unset;

    .el-dialog__header {
      padding-bottom: 0;
    }

    .cm-dialog-box-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0px 1px 0px #ecedef;
      padding-left: 20px;

      .cm-dialog__headerbtn {
        border: none;
        cursor: pointer;
        font-size: var(--el-message-close-size, 18px);
        outline: none;
        padding: 0;
        background: transparent;
        width: 48px;
        padding: 10px 0;

        .el-dialog__close {
          color: #bbb;
        }

        &:hover {
          .el-dialog__close {
            color: var(--el-color-primary);
          }
        }
      }
    }

    .el-dialog__footer {
      box-shadow: 0px -1px 0px #ecedef;
      padding: var(--el-dialog-padding-primary);
    }
  }
}
</style>
