<template>
  <cm-container>
    <div class="container">
      <el-radio-group v-model="activeName" style="margin-left: 10px;">
        <el-radio-button label="first">组织维度分析</el-radio-button>
        <el-radio-button label="second">课程维度分析</el-radio-button>
      </el-radio-group>
      
      <Organization  :data="organizationData" v-if="activeName === 'first'"/>
      <Course :data="courseData" v-if="activeName === 'second'"/>
    </div>
  </cm-container>
</template>

<script setup>
import CmContainer from '@/components/cm-container/main.vue'
import * as echarts from 'echarts';
import Organization from './components/organization.vue'
import Course from './components/course.vue'

const activeName = ref('first')

const organizationData = ref({})
const courseData = ref({})

const handleClick = (tab, event) => {
  // console.log(tab, event)
  
}


</script>

<style lang="scss" scoped>
.container {
}
</style>