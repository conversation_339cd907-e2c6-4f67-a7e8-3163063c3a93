<template>
  <cm-container>
    <div class="alarm-settings-setting-box">
      <div class="alarm-settings-setting">
        <div class="alarm-settings-setting-content">
          <div class="alarm-settings-setting-content-header">
            <div class="alarm-settings-setting-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span class="alarm-settings-setting-content-header-title"
                >非毕业生（预警上学期及之前的课程信息）</span>
              <el-button type="primary" style="position: absolute;right: 0;" @click="onAdd('fbys')"
                >添加时间</el-button>
            </div>
          </div>
          <div class="alarm-settings-setting-content-table">
            <el-table :data="fbysTableData" style="width: 100%">
              <el-table-column type="index" width="55" label="序号" />
              <el-table-column  label="生成预警明细时间" align="center">
                <template #default="scope">
                  <span v-if="scope.row.scyjsj === ''">
                    <el-button type="primary" @click="onAddTime(scope.row,'scyjsj','fbys')">添加时间</el-button>
                  </span>
                  <span v-else>{{ scope.row.scyjsj }} 
                    <el-button type="primary" @click="onAddTime(scope.row, 'scyjsj','fbys')" style="margin-left: 10px;">更改时间</el-button>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="推送预警信息时间" align="center">
                <template #default="scope">
                  <span v-if="scope.row.tsyjxxsj === ''">
                    <el-button type="primary" @click="onAddTime(scope.row, 'tsyjxxsj','fbys')">添加时间</el-button>
                  </span>
                  <span v-else>{{ scope.row.tsyjxxsj }}
                    <el-button type="primary" @click="onAddTime(scope.row, 'tsyjxxsj','fbys')" style="margin-left: 10px;">更改时间</el-button>
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="zymc" label="推送方式" align="center">
                <template #default="scope">
                    <el-checkbox v-model="scope.row.sftsdx" label="1">短信推送</el-checkbox>
                    <el-checkbox v-model="scope.row.sftsznx" label="2">消息中心推送</el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="zymc" label="操作" align="center">
                <template #default="scope">
                  <el-button type="primary" link @click="onDelete(scope.row, 'fbys')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="alarm-settings-setting-content-header" style="margin-top: 20px">
            <div class="alarm-settings-setting-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span class="alarm-settings-setting-content-header-title"
                >毕业生（预警本学期及之前的课程信息）</span>
              <el-button type="primary" style="position: absolute;right: 0;" @click="onAdd('bys')"
              >添加时间</el-button>
            </div>
          </div>

          <div class="alarm-settings-setting-content-table">
            <el-table :data="bysTableData" style="width: 100%">
              <el-table-column type="index" width="55" label="序号" />
              <el-table-column  label="生成预警明细时间" align="center">
                <template #default="scope">
                  <span v-if="scope.row.scyjsj === ''">
                    <el-button type="primary" @click="onAddTime(scope.row, 'scyjsj','bys')">添加时间</el-button>
                  </span>
                  <span v-else>{{ scope.row.scyjsj }}
                    <el-button type="primary" @click="onAddTime(scope.row, 'scyjsj','bys')" style="margin-left: 10px;">更改时间</el-button>
                  </span>
                </template>
              </el-table-column>
              <el-table-column  label="推送预警信息时间" align="center">
                <template #default="scope">
                  <span v-if="scope.row.tsyjxxsj === ''">
                    <el-button type="primary" @click="onAddTime(scope.row, 'tsyjxxsj','bys')">添加时间</el-button>
                  </span>
                  <span v-else>{{ scope.row.tsyjxxsj }}
                    <el-button type="primary" @click="onAddTime(scope.row, 'tsyjxxsj','bys')" style="margin-left: 10px;">更改时间</el-button>
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="zymc" label="推送方式" align="center">
                <template #default="scope">
                    <el-checkbox v-model="scope.row.sftsdx" label="1">短信推送</el-checkbox>
                    <el-checkbox v-model="scope.row.sftsznx" label="2">消息中心推送</el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="zymc" label="操作" align="center">
                <template #default="scope">
                  <el-button type="primary" link @click="onDelete(scope.row, 'bys')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <div class="alarm-settings-setting-box-footer">
      <el-button @click="onCancel" v-loading="saveLoading">取 消</el-button>
      <!-- <el-button @click="onSave" v-loading="saveLoading">保存设置</el-button> -->
      <el-button type="primary" @click="onSave" v-loading="saveLoading">保存并启用</el-button>
    </div>
    <cm-dialog
      v-model="dialogYjsjVisible"
      title="添加预警时间"
      width="400"
      class="alarm-date-dialog"
    >
      <div class="alarm-date-dialog-content">
        <CalendarPanel
          @close="dialogYjsjVisible = false"
          ref="calendarPanelRef"
          :isBatch="false"
          @confirm="onConfirmDate"
        />
      </div>
      <template #footer>
        <el-button @click="onCloseCalendar">取消</el-button>
        <!-- <el-button @click="clearSelection">清空选择</el-button> -->
        <el-button type="primary" @click="onConfirmCalendar"> 确 定 </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>
  
  <script setup>
import { ElMessageBox, ElMessage } from 'element-plus';
import CmContainer from '@/components/cm-container/main.vue';
import CalendarPanel from './components/CalendarPanel.vue';
import CmDialog from '@/components/cm-dialog/index.vue';
import { useRoute, useRouter } from 'vue-router';
import { listTsSj, removeSj, addTsSj, updateByKey } from '@/api/alarmingPush/alarmingPush';
const router = useRouter();
const route = useRoute();


listTsSj().then(res => {
  let data = res.data.data
  data.forEach(item => {
    item.sftsdx = item.sftsdx == 1 ? true : false
    item.sftsznx = item.sftsznx == 1 ? true : false
    if(item.tsdx == 1) {
      fbysTableData.value.push(item)
    } else if(item.tsdx == 2) {
      bysTableData.value.push(item)
    }
  })
  
});
const fbysTableData = ref([]);
const bysTableData = ref([]);
const onAdd = (type) => {
  console.log(type);
  if (type === 'fbys') {
    fbysTableData.value.push({
      // id: fbysTableData.value.length + 1,
      scyjsj: '',
      tsyjxxsj: '',
      sftsdx: false,
      sftsznx: false,

    });
  } else if (type === 'bys') {
    bysTableData.value.push({
      // id: bysTableData.value.length + 1,
      scyjsj: '',
      tsyjxxsj: '',
      sftsdx: false,
      sftsznx: false,
    });
  }
};
const onDelete = (row, type) => {
  if (type === 'fbys') {
    fbysTableData.value = fbysTableData.value.filter(item => item.id !== row.id);
  } else if (type === 'bys') {
    bysTableData.value = bysTableData.value.filter(item => item.id !== row.id);
  }
};
const dialogYjsjVisible = ref(false);
const calendarPanelRef = ref(null);
const typeData = ref('');
const rowData = ref({});
const columnData = ref('');
const onAddTime = (row, column, type) => {
  dialogYjsjVisible.value = true;
  rowData.value = row;
  columnData.value = column;
  typeData.value = type;
};
const onConfirmCalendar = () => {
  calendarPanelRef.value.confirmSelection();
};
const onCloseCalendar = () => {
  calendarPanelRef.value.onClose();
};

const onConfirmDate = (data) => {
  if (typeData.value === 'fbys') {
    fbysTableData.value.forEach(item => {
      if (item.id === rowData.value.id) {
        item[columnData.value] = data.start +' '+ data.hour + '点';
      }
    });
  } else if (typeData.value === 'bys') {
    bysTableData.value.forEach(item => {
      if (item.id === rowData.value.id) {
        item[columnData.value] = data.start +' '+ data.hour + '点';
      }
    });
  }
};


// 取消
const onCancel = () => {
  router.back();
};
//将这个函数修改正确
const judgeNull = () => {
  // 检查非毕业生数据是否有空值
  for (let item of fbysTableData.value) {
    if (!item.scyjsj || !item.tsyjxxsj) {
      return false; // 有空值，返回false
    }
  }
  
  // 检查毕业生数据是否有空值
  for (let item of bysTableData.value) {
    if (!item.scyjsj || !item.tsyjxxsj) {
      return false; // 有空值，返回false
    }
  }
  
  return true; // 所有数据都完整，返回true
}
// 保存修改的数据并提交到服务器
const saveLoading = ref(false);
const onSave = () => {
  saveLoading.value = true;
  let isValid = judgeNull()
  console.log(isValid,'isValid');
  
  if(!isValid) {
    ElMessage.error('请添加预警时间')
    saveLoading.value = false;
    return
  }
  let data = fbysTableData.value.map(item => {
    return {
      ...item,
      sftsdx: item.sftsdx ? 1 : 0,
      sftsznx: item.sftsznx ? 1 : 0,
      tdx: 1,
    }
  })
  let data2 = bysTableData.value.map(item => {
    return {
      ...item,
      sftsdx: item.sftsdx ? 1 : 0,
      sftsznx: item.sftsznx ? 1 : 0,
      tsdx: 2,
    }
  })
  data.push(...data2)
  addTsSj(data).then(res => {
    updateByKey({
      paramKey: 'alarm.auto',
      paramValue: 1,
    }).then(result => {
      onCancel()
    });
  }).finally(() => {
      saveLoading.value = false;
    });
};
</script>
  
  <style lang="scss" scoped>
.alarm-settings-setting-box {
  height: calc(100% - 70px);
  overflow-y: auto;

  .alarm-settings-setting {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 2px solid #ffffff;
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    .alarm-settings-setting-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .alarm-settings-setting-header-left {
        display: flex;
        align-items: center;
        gap: 14px;

        .alarm-settings-setting-header-icon {
          width: 24px;
          height: 24px;
        }

        .alarm-settings-setting-header-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }
      }

      .alarm-settings-setting-header-right {
        display: flex;
        align-items: center;
      }
    }

    .alarm-settings-setting-content {
      background: #fcfcfc;
      border-radius: 4px;
      padding: 20px;
      box-sizing: border-box;
      height: 100%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .alarm-settings-setting-content-header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .alarm-settings-setting-content-header-title {
          font-family: AppleSystemUIFont;
          font-size: 13px;
          color: #333333;
        }

        .alarm-settings-setting-content-header-title-box {
          display: flex;
          align-items: center;
          gap: 6px;
        }
      }

      .alarm-settings-setting-content-table {
        margin-top: 20px;
      }
    }
  }

  .mt-20 {
    margin-bottom: 20px;
  }
}

.alarm-settings-setting-box-footer {
  margin-top: 10px;
  height: 60px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-sizing: border-box;
}

.alarm-date-dialog {
  .alarm-date-dialog-content {
    padding: 20px;
  }
}
</style>
  