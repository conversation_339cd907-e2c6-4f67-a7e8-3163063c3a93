<template>
  <div class="info-step">
    <div
      class="alarm-settings"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50">
      <div class="alarm-settings-search">
        <!-- <el-select style="width: 200px" v-model="searchParams.yx" placeholder="全部院系" clearable>
          <el-option v-for="item in yxList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 200px" v-model="searchParams.zy" placeholder="全部专业" clearable>
          <el-option v-for="item in zyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 200px" v-model="searchParams.nj" placeholder="全部年级" clearable>
          <el-option v-for="item in njList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-select style="width: 200px" v-model="searchParams.sfyd" placeholder="全部异动类型" clearable>
          <el-option v-for="item in sfydList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <!-- <el-select style="width: 200px" v-model="searchParams.bj" placeholder="全部班级" clearable>
          <el-option v-for="item in bjList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
        <el-input style="width: 200px" v-model="searchParams.xsxm" placeholder="请输入姓名" clearable> </el-input>
        <div class="alarm-settings-search-button">
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
        <el-button type="primary" align="right" @click="onBack">返回上级</el-button>
      </div>
      <div class="alarm-settings-table" ref="tableRef">
        <div class="alarm-settings-table-header">
          <div class="alarm-settings-table-header-item">
            <div class="alarm-settings-table-header-item-title">
              <el-icon color="#2d55eb" size="16">
                <Document />
              </el-icon>
              <span class="alarm-settings-table-header-title">必修+实习+毕设课程信息</span>
            </div>
            <div class="alarm-settings-table-header-item-content">
              <el-table :data="filteredTableData" border size="small" v-loading="tableDataLoading" @selection-change="onSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column prop="xymc" label="预警院系" />
                <el-table-column prop="zymc" label="预警专业" />
                <el-table-column prop="nj" label="预警年级" />
                <el-table-column prop="bjmc" label="预警班级" />
                <el-table-column prop="xsxh" label="学号" />
                <el-table-column prop="xsxm" label="姓名" />
                <el-table-column prop="sfyd" label="异动类型" />
                <el-table-column prop="kcxzmc" label="课程性质" />
                <el-table-column prop="kcsxmc" label="课程属性" />
                <el-table-column prop="kcbh" label="课程号" />
                <el-table-column prop="kcmc" label="课程" />
                <el-table-column prop="sdxf" label="实得学分" />
                <el-table-column prop="sdcj" label="实得成绩" />
                <el-table-column prop="sfbg" label="是否必过" />
                <el-table-column prop="remark" label="备注" />
                <el-table-column prop="hyzt" label="核验结果">
                  <template #default="scope">
                    <span v-if="scope.row.hyzt == 1">通过并预警</span>
                    <span v-if="scope.row.hyzt == 2">无需预警</span>
                  </template>
                </el-table-column>
                <el-table-column label="推送状态" prop="sfts" fixed="right"></el-table-column>
                <el-table-column label="预警推送时间" prop="tssj" fixed="right"></el-table-column>
                <el-table-column label="推送方式" fixed="right">
                  <template #default="scope">
                    <span>{{ scope.row.sftsdx == 1 ? (scope.row.sftsznx == 1 ? "短信+平台" : "短信") : scope.row.sftsznx == 0 ? "平台" : "-" }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div class="alarm-settings-table-header">
          <div class="alarm-settings-table-header-item">
            <div class="alarm-settings-table-header-item-title">
              <el-icon color="#2d55eb" size="16">
                <Document />
              </el-icon>
              <span class="alarm-settings-table-header-title">选修课程信息</span>
            </div>
            <div class="alarm-settings-table-header-item-content">
              <el-table :data="filteredTableSelectData" border size="small" v-loading="tableSelectDataLoading" @selection-change="onSelectionSelectChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column prop="xymc" label="预警院系" />
                <el-table-column prop="zymc" label="预警专业" />
                <el-table-column prop="nj" label="预警年级" />
                <el-table-column prop="bjmc" label="预警班级" />
                <el-table-column prop="xsxh" label="学号" />
                <el-table-column prop="xsxm" label="姓名" />
                <el-table-column prop="sfyd" label="异动类型" />
                <el-table-column prop="kcsxmc" label="课程属性" />
                <el-table-column prop="xxhgs" label="已修课程" />
                <el-table-column prop="xxqks" label="待修课程" />
                <el-table-column prop="hyzt" label="核验结果">
                  <template #default="scope">
                    <span v-if="scope.row.hyzt == 1">通过并预警</span>
                    <span v-if="scope.row.hyzt == 2">无需预警</span>
                  </template>
                </el-table-column>
                <el-table-column label="推送状态" prop="sfts" fixed="right"></el-table-column>
                <el-table-column label="预警推送时间" prop="tssj" fixed="right"></el-table-column>
                <el-table-column label="推送方式" fixed="right">
                  <template #default="scope">
                    <span>{{ scope.row.sftsdx == 1 ? (scope.row.sftsznx == 1 ? "短信+平台" : "短信") : scope.row.sftsznx == 0 ? "平台" : "-" }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { checkStudentList, saveCheckData, push } from "@/api/alarmingPush/alarmingPush";
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `;
const searchParams = ref({
  sfyd: "",
  yjlx: "",
  xsxm: "",
});

const sfydList = ref([
  {
    value: "全部异动类型",
    label: "全部异动类型",
  },
]);

const selectionData = ref([]);
const onSelectionChange = (val) => {
  selectionData.value = val;
};
const selectionSelectData = ref([]);
const onSelectionSelectChange = (val) => {
  selectionSelectData.value = val;
};

const handleOnPush = (checkedPush) => {
  let data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  let pushList = selectionData.value.map((item) => {
    return {
      yjbh: item.yjbh,
      yxbh: item.yxbh,
      zybh: item.zybh,
      xsxh: item.xsxh,
      xsxm: item.xsxm,
      nj: item.nj,
      bjbh: item.bjbh,
    };
  });
  let pushListSelect = selectionSelectData.value.map((item) => {
    return {
      yjbh: item.yjbh,
      yxbh: item.yxbh,
      zybh: item.zybh,
      xsxh: item.xsxh,
      xsxm: item.xsxm,
      nj: item.nj,
      bjbh: item.bjbh,
    };
  });
  let pushData = {
    tsfw: 3,
    yjbh: data.data.yjbh,
    sftsdx: checkedPush.includes(1) ? 1 : 0,
    sftsznx: checkedPush.includes(2) ? 1 : 0,
    pushList: [...pushList, ...pushListSelect],
  };
  push(pushData).then((res) => {
    getTableData();
  });
};

const handleOnSave = () => {
  let data = [];
  tableData.value.forEach((item) => {
    data.push({
      // xsxh: item.xsxh,
      hyzt: item.hyzt,
      id: item.id,
      // yjbh: item.yjbh,
    });
  });
  tableSelectData.value.forEach((item) => {
    data.push({
      xsxh: item.xsxh,
      hyzt: item.hyzt,
      // id: item.id,
      yjbh: item.yjbh,
    });
  });
  saveCheckData(data).then((res) => {
    // console.log(res);
    getTableData();
  });
};
defineExpose({ handleOnSave, handleOnPush });
const onSearch = () => {
  // 执行本地筛选
  performLocalFilter();

  // console.log(filteredTableData.value);
  // console.log(filteredTableSelectData.value);
};
const onClear = () => {
  searchParams.value = {};
  // 清空后显示所有数据
  filteredTableData.value = [...tableData.value];
  filteredTableSelectData.value = [...tableSelectData.value];
};
const radio1 = ref("1");
const loading = ref(false);
const tableData = ref([]);
const tableDataLoading = ref(false);
const tableSelectData = ref([]);
const tableSelectDataLoading = ref(false);

// 修改为响应式数据而不是计算属性
const filteredTableData = ref([]);
const filteredTableSelectData = ref([]);

// 添加本地筛选函数
const performLocalFilter = () => {
  // 筛选必修+实习+毕设课程信息
  if (!searchParams.value.xsxm && !searchParams.value.sfyd) {
    filteredTableData.value = [...tableData.value];
  } else {
    filteredTableData.value = tableData.value.filter((item) => {
      let match = true;

      // 按姓名筛选
      if (searchParams.value.xsxm && item.xsxm) {
        match = match && item.xsxm.includes(searchParams.value.xsxm);
      }

      // 按异动类型筛选
      if (searchParams.value.sfyd && searchParams.value.sfyd !== "全部异动类型") {
        match = match && item.sfyd === searchParams.value.sfyd;
      }

      return match;
    });
  }

  // 筛选选修课程信息
  if (!searchParams.value.xsxm && !searchParams.value.sfyd) {
    filteredTableSelectData.value = [...tableSelectData.value];
  } else {
    filteredTableSelectData.value = tableSelectData.value.filter((item) => {
      let match = true;

      // 按姓名筛选
      if (searchParams.value.xsxm && item.xsxm) {
        match = match && item.xsxm.includes(searchParams.value.xsxm);
      }

      // 按异动类型筛选
      if (searchParams.value.sfyd && searchParams.value.sfyd !== "全部异动类型") {
        match = match && item.sfyd === searchParams.value.sfyd;
      }

      return match;
    });
  }
};

const getTableData = () => {
  let data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  let params = {
    // nj: data.data.nj,
    // zybh: data.data.zybh,
    yjbh: data.data.yjbh,
    bjbh: data.data.bjbh,
  };
  tableDataLoading.value = true;
  tableSelectDataLoading.value = true;
  // loading.value = true;
  checkStudentList(Object.assign(params, searchParams.value))
    .then((res) => {
      tableData.value = res.data.data;
      // 初始化筛选后的数据
      filteredTableData.value = [...res.data.data];
    })
    .finally(() => {
      tableDataLoading.value = false;
      // loading.value = false;
    });
  checkStudentList(Object.assign(params, searchParams.value, { yjlx: 4 }))
    .then((res) => {
      tableSelectData.value = res.data.data;
      // 初始化筛选后的数据
      filteredTableSelectData.value = [...res.data.data];
    })
    .finally(() => {
      tableSelectDataLoading.value = false;
    });
};
getTableData();

const emit = defineEmits(["changeActive"]);
const onBack = () => {
  let data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  sessionStorage.setItem("alarmingPushSearchData", JSON.stringify({ data: data.data, activeInIndex: 1, active: 2 }));
  emit("changeActive", {
    active: 2,
    activeInIndex: 1,
    data: {},
  });
};
</script>
<style lang="scss" scoped>
.info-step {
  .alarm-settings {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 0 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .alarm-settings-search {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 10px;

      .alarm-settings-search-button {
        display: flex;
        align-items: center;
      }
    }

    .alarm-settings-operations {
      display: flex;
      align-items: center;
    }

    .alarm-settings-table {
      height: calc(100vh - 410px);
      width: 100%;
      overflow-y: auto;
      .alarm-settings-table-header {
        .alarm-settings-table-header-item {
          box-sizing: border-box;
          .alarm-settings-table-header-item-title {
            display: flex;
            margin-top: 10px;
            gap: 5px;
            .alarm-settings-table-header-title {
              font-size: 12px;
              margin-bottom: 10px;
            }
          }
          .alarm-settings-table-header-item-content {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
