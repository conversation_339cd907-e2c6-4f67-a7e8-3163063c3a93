<template>
  <div>
    <div>
      <el-form :model="form" label-width="100px" inline>
        <!-- <el-form-item label="推送时间">
                    <el-date-picker
                        v-model="value1"
                        type="datetime"
                        clearable
                        placeholder="选择日期时间">
                    </el-date-picker>
                </el-form-item> -->
        <el-form-item label="推送方式">
          <el-checkbox-group v-model="checkedPush" @change="handleCheckedCitiesChange">
            <el-checkbox label="短信推送" :value="1"></el-checkbox>
            <el-checkbox label="消息中心推送" :value="2"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <!-- <div class="warning-tip">
        <span> 成绩异常（无成绩）的人员已进行核验，是否仍要推送告警。 </span>
        <el-radio v-model="radio" label="1">不推送</el-radio>
        <el-radio v-model="radio" label="2">需要推送</el-radio>
      </div> -->
      <step2NJ v-if="active == 1" @changeActive="handleChangeActive" ref="step2NJRef" />
      <step2BJ v-if="active == 2" @changeActive="handleChangeActive" ref="step2BJRef" />
      <step2XS v-if="active == 3" @changeActive="handleChangeActive" ref="step2XSRef" />
    </div>
  </div>
</template>
<script setup>
import step2NJ from "@/views/alarmingPush/components/step2NJ.vue";
import step2XS from "@/views/alarmingPush/components/step2XS.vue";
import step2BJ from "@/views/alarmingPush/components/step2BJ.vue";
const props = defineProps({
  activeInStep1: {
    type: Number,
    default: 1,
  },
});
const active = ref(props.activeInStep1);
const handleChangeActive = (data) => {
  console.log(data);
  active.value = data.active;
};
// const value1 = ref(""); // 选择日期时间
// const radio = ref("1"); // 推送方式
const checkedPush = ref([]); // 推送方式
const step2NJRef = ref(null);
const step2BJRef = ref(null);
const step2XSRef = ref(null);
const handleOnPush = () => {
  if (active.value == 1) {
    step2NJRef.value.handleOnPush(checkedPush.value);
  } else if (active.value == 2) {
    step2BJRef.value.handleOnPush(checkedPush.value);
  } else if (active.value == 3) {
    step2XSRef.value.handleOnPush(checkedPush.value);
  }
};
defineExpose({ handleOnPush });
</script>

<style lang="scss" scoped>
.warning-tip {
  background-color: #ffecec;
  color: #f56c6c;
  padding: 8px 12px;
  box-sizing: border-box;
  font-size: 14px;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
</style>
