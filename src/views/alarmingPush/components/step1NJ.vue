<template>
  <div class="info-step">
    <div
      class="alarm-settings"
      element-loading-text="加载中..."
      v-loading="loading"
      :element-loading-svg="svg"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      element-loading-svg-view-box="-10, -10, 50, 50">
      <div class="alarm-settings-search">
        <el-select style="width: 220px" v-model="searchParams.yxbh" placeholder="全部院系" clearable>
          <el-option v-for="item in yxList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 220px" v-model="searchParams.zybh" placeholder="全部专业" clearable>
          <el-option v-for="item in zyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 220px" v-model="searchParams.nj" placeholder="全部年级" clearable>
          <el-option v-for="item in njList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select style="width: 220px" v-model="searchParams.yjlx" placeholder="全部类型" clearable>
          <el-option v-for="item in yjList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <div class="alarm-settings-search-button">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="alarm-settings-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column type="index" width="55" align="center" />
          <el-table-column prop="xymc" label="预警院系" />
          <el-table-column prop="zymc" label="预警专业" />
          <el-table-column prop="nj" label="预警年级" />
          <el-table-column prop="csyjlxmc" label="产生预警类型" />
          <el-table-column prop="yjrs" label="预警人数" />
          <el-table-column prop="yjkcs" label="预警课程数" />
          <el-table-column prop="remark" label="备注" />
          <el-table-column prop="hyzt" label="核验状态">
            <!-- 0:待核验 1：已核验 -->
            <template #default="scope">
              <span v-if="scope.row.hyzt === '0'">待核验</span>
              <span v-if="scope.row.hyzt === '1'">已核验</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="onJumpInfo(scope.row)">查看明细</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script setup>
import { pageByMajor } from "@/api/alarmingPush/alarmingPush";
import useBusinessData from "@/hooks/useBusinessData";

const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `;
const { yxList, zyList, njList } = useBusinessData("yx", "zy", "nj");
const yjList = ref([]);
const searchParams = ref({
  yxbh: "",
  zybh: "",
  nj: "",
  yjlx: "",
});

const tableData = ref([]);

const onSearch = () => {
  getList();
};
const onClear = () => {
  searchParams.value = {};
  getList();
};
const loading = ref(false);
const getList = () => {
  let data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  let searchData = {
    yjbh: data.data.yjbh,
  };
  loading.value = true;
  pageByMajor(Object.assign(searchData, searchParams.value))
    .then((res) => {
      tableData.value = res.data.data;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();
const emit = defineEmits(["changeActive"]);
const onJumpInfo = (row) => {
  let data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  sessionStorage.setItem(
    "alarmingPushSearchData",
    JSON.stringify({
      data: {
        nj: row.nj,
        zybh: row.zybh,
        yjbh: data.data.yjbh,
      },
      active: 2,
      activeInIndex: 0,
    }),
  );
  emit("changeActive", {
    active: 2,
    data: row,
  });
};
</script>
<style lang="scss" scoped>
.info-step {
  .alarm-settings {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 0 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .alarm-settings-search {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 10px;

      .alarm-settings-search-button {
        display: flex;
        align-items: center;
      }
    }

    .alarm-settings-operations {
      display: flex;
      align-items: center;
    }

    .alarm-settings-table {
      height: calc(100vh - 360px);
      overflow-y: auto;
    }
  }
}
</style>
