<template>
  <div>
    <step1NJ v-if="active == 1" @changeActive="handleChangeActive" />
    <step1BJ v-if="active == 2" @changeActive="handleChangeActive" />
    <step1XS v-if="active == 3" @changeActive="handleChangeActive" ref="step1XSRef" />
  </div>
</template>
<script setup>
import { defineExpose } from "vue";
import step1NJ from "@/views/alarmingPush/components/step1NJ.vue";
import step1XS from "@/views/alarmingPush/components/step1XS.vue";
import step1BJ from "@/views/alarmingPush/components/step1BJ.vue";

const props = defineProps({
  activeInStep1: {
    type: Number,
    default: 1,
  },
});
const active = ref(props.activeInStep1);
const emit = defineEmits(["changeActive"]);
const handleChangeActive = (data) => {
  active.value = data.active;
  emit("changeActive", data);
};
const step1XSRef = ref(null);
const handleOnSave = () => {
  step1XSRef.value.handleOnSave();
};
defineExpose({ handleOnSave });
</script>

<style lang="scss" scoped></style>
