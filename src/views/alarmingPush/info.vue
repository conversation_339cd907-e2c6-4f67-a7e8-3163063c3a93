<template>
  <cm-container class="info-container">
    <div class="header">
      <div class="header-steps">
        <el-steps :active="activeInIndex" process-status="finish " align-center style="height: 55px">
          <el-step title="预警信息核验"></el-step>
          <el-step title="预警信息推送"></el-step>
        </el-steps>
      </div>
      <div class="content">
        <div class="content-title">
          <el-icon color="#2d55eb" size="18">
            <Document />
          </el-icon>
          <span v-if="activeInIndex == 0">预警信息核验</span>
          <span v-if="activeInIndex == 1">预警推送明细</span>
        </div>
        <div class="content-table">
          <info-step1 v-if="activeInIndex == 0" @changeActive="handleChangeActive" :activeInStep1="activeInStep1" ref="infoStep1Ref" />
          <info-step2 v-if="activeInIndex == 1" @changeActive="handleChangeActive" :activeInStep1="activeInStep1" ref="infoStep2Ref" />
        </div>
        <div class="content-footer">
          <el-button @click="handleBack">返回列表</el-button>
          <el-button type="primary" @click="handleNext" v-if="activeInIndex == 0 && activeInStep1 == 1">下一步</el-button>
          <el-button type="primary" @click="handleSave" v-if="activeInIndex == 0 && activeInStep1 == 3">保存核验结果</el-button>
          <el-button type="primary" @click="handlePrev" v-if="activeInIndex == 1">上一步</el-button>
          <el-button type="primary" @click="handlePush" v-if="activeInIndex == 1">推送告警</el-button>
        </div>
      </div>
    </div>
  </cm-container>
</template>
<script setup>
import CmContainer from "@/components/cm-container/main.vue";
import infoStep1 from "@/views/alarmingPush/components/infoStep1.vue";
import infoStep2 from "@/views/alarmingPush/components/infoStep2.vue";
import { useRouter } from "vue-router";
const activeInIndex = ref(0); //核验步骤
const router = useRouter();
//返回列表
const handleBack = () => {
  router.push("/alarmingPush");
};
const activeInStep1 = ref(1); //核验步骤
//核验步骤
const handleChangeActive = (data) => {
  activeInStep1.value = data.active;
};
const getActive = () => {
  const data = JSON.parse(sessionStorage.getItem("alarmingPushSearchData"));
  activeInIndex.value = data.activeInIndex;
  activeInStep1.value = data.active;
};
getActive();
//下一步
const handleNext = () => {
  activeInIndex.value = 1;
};
//上一步
const handlePrev = () => {
  activeInIndex.value = 0;
};
let infoStep2Ref = ref(null);
//推送告警
const handlePush = () => {
  infoStep2Ref.value.handleOnPush();
};
let infoStep1Ref = ref(null);
//保存核验结果
const handleSave = () => {
  infoStep1Ref.value.handleOnSave();
};
onUnmounted(() => {
  sessionStorage.removeItem("alarmingPushSearchData");
});
</script>
<style scoped lang="scss">
.info-container {
  padding: 10px;
  .header {
    padding-top: 10px;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    .header-steps {
      height: 55px;
    }
    .content {
      width: 100%;
      box-sizing: border-box;
      height: calc(100% - 45px);
      padding: 0 20px;
      .content-title {
        height: 50px;
        display: flex;
        align-items: center;
        gap: 5px;
      }
      .content-table {
        height: calc(100% - 110px);
      }
      .content-footer {
        height: 60px;
        z-index: 9000;
        box-sizing: border-box;
        position: fixed;
        bottom: 10px;
        left: 260px;
        right: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
