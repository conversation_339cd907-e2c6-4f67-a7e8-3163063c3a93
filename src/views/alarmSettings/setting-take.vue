<template>
  <cm-container>
    <div class="alarm-settings-setting-take-take-box">
      <div class="alarm-settings-setting-take">
        <div class="alarm-settings-setting-take-header">
          <div class="alarm-settings-setting-take-header-left">
            <img class="alarm-settings-setting-take-header-icon" src="/img/alarmAnalyse/card-icon.png" alt="" />
            <span class="alarm-settings-setting-take-header-title">专业信息</span>
          </div>
        </div>
        <div class="alarm-settings-setting-take-content">
          <el-table :data="zyTableData" style="width: 100%">
            <el-table-column prop="nj" label="年级" align="center" width="150" />
            <el-table-column prop="zyssyxbmc" label="院系" align="center" />
            <el-table-column prop="zymc" label="专业" align="center" width="200" />
          </el-table>
        </div>
      </div>

      <!-- 基础设置 -->
      <div class="alarm-settings-setting-take">
        <div class="alarm-settings-setting-take-header">
          <div class="alarm-settings-setting-take-header-left">
            <img class="alarm-settings-setting-take-header-icon" src="/img/alarmAnalyse/card-icon.png" alt="" />
            <span class="alarm-settings-setting-take-header-title">课程设置</span>
          </div>
          <el-button type="primary" @click="dialogKcVisible = true">添加条件</el-button>
        </div>
        <div class="alarm-settings-setting-take-content">
          <div class="alarm-settings-setting-content-table">
            <el-table :data="kcTableData" style="width: 100%">
              <el-table-column prop="xxlx" label="课程性质" :formatter="(row) => dimTyglXxkclxDictData.find((item) => item.value === row.xxlx)?.label" />
              <el-table-column prop="msyq" label="课程门数要求（＞＝）" />
              <el-table-column prop="fsyq" label="课程学分要求（＞＝）" />
              <el-table-column label="操作" width="80" align="center">
                <template #default="scope">
                  <el-button type="primary" link @click="onDeleteAlarmList(scope.row.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="alarm-settings-setting-take-box-footer">
      <el-button @click="onCancel" v-loading="saveLoading">取 消</el-button>
      <el-button type="primary" @click="onSave" v-loading="saveLoading">保存</el-button>
    </div>
    <cm-dialog v-model="dialogKcVisible" title="设置" width="500px" :close-on-click-modal="false" :close-on-press-escape="false" @close="onCloseKcDialog" class="cm-dialog">
      <template #default>
        <div class="alarm-settings-dialog-content">
          <el-form ref="takeFormKcRef" :model="kcForm" label-width="auto">
            <el-form-item label="课程类型" prop="xxlx">
              <el-select v-model="kcForm.xxlx" placeholder="请选择课程类型">
                <el-option v-for="item in dim_tygl_xxkclxDictData" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="课程门数要求" prop="dgsxfs">
              <el-input-number v-model="kcForm.msyq" :min="0" :max="100" class="inp-text-left" :controls="false" placeholder="请输入课程门数要求（大于等于）" />
            </el-form-item>
            <el-form-item label="课程学分要求" prop="dgsxfs">
              <el-input-number v-model="kcForm.fsyq" :min="0" :max="100" class="inp-text-left" :controls="false" placeholder="请输入课程学分要求（大于等于）" />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="onCloseKcDialog">取消</el-button>
        <el-button type="primary" @click="onConfirmKc"> 确 定 </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import CmContainer from "@/components/cm-container/main.vue";
import CmDialog from "@/components/cm-dialog/index.vue";
import useDict from "@/hooks/useDict";
import { useRoute, useRouter } from "vue-router";
import { getZyDetailAPI, saveXxKcAPI, getXxKcAPI } from "@/api/alarmSettings";
import { ElMessage } from "element-plus";

defineOptions({
  name: "AlarmSettingsSettingTake",
});
const router = useRouter();
const route = useRoute();
const { dimTyglXxkclxDictData } = useDict("dim_tygl_xxkclx");

// 获取专业信息
const zyTableData = ref([]);
getZyDetailAPI({
  zyh: route.query.zyh,
}).then((reslut) => {
  const { code, data } = reslut.data || {};
  if (code === 200) {
    zyTableData.value = [
      {
        zyssyxbmc: data.zyssyxbmc,
        zymc: data.zymc,
        nj: route.query.nj,
      },
    ];
  }
});

// 获取课程设置
if (route.query.id) {
  getXxKcAPI({
    sswYjId: route.query.id,
  }).then((reslut) => {
    const { code, data } = reslut.data || {};
    if (code === 200) {
      kcTableData.value = data;
    }
  });
}

// 课程设置
const kcTableData = ref([]);

// 保存按钮loading
const saveLoading = ref(false);

// 添加条件弹窗
const dialogKcVisible = ref(false);
const kcForm = ref({
  xxlx: "",
  msyq: null,
  fsyq: null,
});
const takeFormKcRef = useTemplateRef("takeFormKcRef");
const onConfirmKc = () => {
  takeFormKcRef.value.validate((valid) => {
    if (valid) {
      kcTableData.value.push({ ...kcForm.value });
      dialogKcVisible.value = false;
    }
  });
};

const onCloseKcDialog = () => {
  takeFormKcRef.value.resetFields();
  kcForm.value = {
    xxlx: "",
    msyq: null,
    fsyq: null,
  };
  dialogKcVisible.value = false;
};

// 删除条件
const onDeleteAlarmList = (index) => {
  kcTableData.value.splice(index, 1);
};

// 取消
const onCancel = () => {
  router.back();
};

// 保存
const onSave = () => {
  saveLoading.value = true;
  saveXxKcAPI({
    sswYjId: route.query.id,
    zyh: route.query.zyh,
    nj: route.query.nj,
    yjXxList: kcTableData.value,
  }).then((reslut) => {
    const { code, data } = reslut.data || {};
    if (code === 200) {
      saveLoading.value = false;
      router.back();
      ElMessage.success("保存成功");
    }
  });
};
</script>

<style scoped lang="scss">
.alarm-settings-setting-take-take-box {
  height: calc(100% - 70px);
  overflow-y: auto;

  .alarm-settings-setting-take {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 2px solid #ffffff;
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    .alarm-settings-setting-take-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .alarm-settings-setting-take-header-left {
        display: flex;
        align-items: center;
        gap: 14px;

        .alarm-settings-setting-take-header-icon {
          width: 24px;
          height: 24px;
        }

        .alarm-settings-setting-take-header-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }
      }

      .alarm-settings-setting-take-header-right {
        display: flex;
        align-items: center;
      }
    }

    .alarm-settings-setting-take-content {
      background: #fcfcfc;
      border-radius: 4px;
      padding: 20px;
      box-sizing: border-box;
      height: 100%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .alarm-settings-setting-take-content-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .alarm-settings-setting-take-content-header-title {
          font-family: AppleSystemUIFont;
          font-size: 13px;
          color: #333333;
        }

        .alarm-settings-setting-take-content-header-title-box {
          display: flex;
          align-items: center;
          gap: 6px;
        }
      }

      .alarm-settings-setting-take-content-table {
        margin-top: 20px;
      }
    }
  }

  .mt-20 {
    margin-bottom: 20px;
  }
}

.alarm-settings-setting-take-box-footer {
  margin-top: 10px;
  height: 60px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-sizing: border-box;
}

.alarm-date-dialog {
  .alarm-date-dialog-content {
    padding: 20px;
  }
}

.cm-dialog {
  .alarm-settings-dialog-content {
    padding: 20px;
  }

  .alarm-date {
    background: #f9faff;
    border: 1px solid #ffffff;
    backdrop-filter: blur(10px);
    padding: 10px;
    box-sizing: border-box;

    .alarm-date-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .alarm-date-header-title {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #333333;
      }
    }

    .alarm-date-table {
      margin-top: 10px;
    }
  }
}
</style>
