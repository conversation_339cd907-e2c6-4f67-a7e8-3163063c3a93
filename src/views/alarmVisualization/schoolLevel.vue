<!-- 校级预警可视化 -->
<template>
  <div class="alarm-visualization">
    <!-- 顶部标题和选择器 -->
    <div class="header-main-bg">
      <div class="head-title">学情预警可视化</div>
      <div class="title-selectors">
         <div class="left-title">
        <div>四川邮电职业技术学院</div>
      </div>
      <div class="selectors">
        <div class="selector-item">
          <el-select v-model="searchParams.xn" placeholder="请选择年份" class="custom-select">
            <el-option label="2025年" value="2025"></el-option>
            <el-option label="2024年" value="2024"></el-option>
            <el-option label="2023年" value="2023"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="searchParams.xq" placeholder="请选择学期" class="custom-select">
            <el-option label="第一学期" value="1"></el-option>
            <el-option label="第二学期" value="2"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="searchParams.nj" placeholder="请选择年级" class="custom-select">
              <el-option
            v-for="item in njList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
          </el-select>
        </div>
      </div>
      </div>
    <!-- 主体内容 -->
     <div class="main-content">
       <!-- 各学院预警学生分布 -->
        <div class="schoolLevel-card">
          <div class="schoolLevel-card-title">各学院预警学生分布</div>
          <div class="card-content">
            <schoolClassAlarm 
            v-if="schoolClassAlarmData.yxScreenVOList.length > 0"
             :data="schoolClassAlarmData" ref="schoolClassAlarmRef" style="width: 100%; height: 100%;"/>
          </div>
        </div>
        <!-- 全校预警概况 -->
        <div class="schoolLevel-card-big ">
          <div class="schoolLevel-card-title-big">全校预警概况</div>
          <div class="card-content">
             <schoolAlarm :data="schoolAlarmData" />
          </div>
        </div>
        <!-- 学院近5年预警趋势 -->
        <div class="schoolLevel-card ">
          <div class="schoolLevel-card-title">各学院近5年预警趋势</div>
          <div class="card-content">
            <schoolAlarmTrend :data="schoolAlarmTrendData" ref="schoolAlarmTrendRef" style="width: 100%; height: 100%;" />
          </div>
        </div>
     </div>
       
     </div>
      <!-- 主体内容 -->
       <div class=" main-content-1">
        <!-- 各学院预警处理情况 -->
        <div class="schoolLevel-card handling-situation">
          <div class="schoolLevel-card-title">各学院预警处理情况</div>
          <div class="card-content1">
            <schoolAlarmHandle :data="schoolAlarmHandleData" ref="schoolAlarmHandleRef" style="width: 100%; height: 100%;" />
          </div>
         
        </div>
        <!-- 各学院预警率排序 -->
        <div class="schoolLevel-card-big ranking">
          <div class="schoolLevel-card-title">各学院预警率排序</div>
          <div class="card-content1">
            <schoolAlarmsort v-if="schoolAlarmsortData && schoolAlarmsortData.length > 0" :data="schoolAlarmsortData"  ref="schoolAlarmsortRef" style="width: 100%; height: 100%;" />
          </div>
        </div>
        <!-- 学院预警专业 TOP5 -->
        <div class="schoolLevel-card top5-profession">
          <div class="schoolLevel-card-title">学院预警专业 <span class="title-top">TOP<span class="title-top-yellow">5</span></span> </div>
          <div class="card-content1">
            <alarmMajorTop5 :data="alarmMajorTop5Data" ref="alarmMajorTop5Ref" style="width: 100%; height: 100%;" />
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, onMounted ,onUnmounted} from 'vue';
import * as echarts from 'echarts';
import schoolAlarmHandle from './components/schoolAlarmHandle.vue'
import schoolClassAlarm from './components/schoolClassAlarm.vue'
import schoolAlarm from './components/schoolAlarm.vue'
import schoolAlarmTrend from './components/schoolAlarmTrend.vue'
import schoolAlarmsort from './components/schoolAlarmsort.vue'
import alarmMajorTop5 from './components/alarmMajorTop5.vue'
import {alarmDistribution,alarmHandle,yxAlarmRank,xxAlarmTrends,alarmZyRank,comData} from '@/api/alarmVisualization';
import useBusinessData from '@/hooks/useBusinessData';
const { njList, } = useBusinessData('nj', );
const searchParams = ref({
  nj: '',
  xn: '',
  xq: '1',
});
// 初始化图表
onMounted(() => {
  getYxAlarmRank()
  getAlarmDistribution()
  getXxAlarmTrends()
  getAlarmHandle()
  getAlarmZyRank()
  getComData()
});
onUnmounted(() => {
  
});
//学院预警概况
const schoolAlarmData =ref({})
const getComData = () => {
  comData(searchParams.value)
    .then(result => {
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,code);
        
        schoolAlarmData.value = data;
      }
    })
}

//学院预警专业 TOP5
const alarmMajorTop5Data =ref(
  [
    {
        name: '人工智能',
        value: 10,
    },
    {
        name: '物联网工程',
        value: 20,
    },
    {
        name: '现代物业管理',
        value: 30,
    },
    {
        name: '软件技术',
        value: 40,
    },
    {
        name: '计算机网络',
        value: 40,
    }
  ]
)
const getAlarmZyRank = () => {
  alarmZyRank(searchParams.value)
    .then(result => {
      console.log(result,'result学院预警专业 TOP5');
      const { data, code } = result.data || {};
      if (code === 200) {
  console.log(data,'data学院预警专业 TOP5');
        alarmMajorTop5Data.value = data;
      }
    })
}

//各学院预警率排序
const schoolAlarmsortData =ref([])
const getYxAlarmRank = () => {
  yxAlarmRank(searchParams.value)
    .then(result => {
      const { data, code } = result.data || {};
      if (code === 200) {
        schoolAlarmsortData.value = data;
      }
    })
}

// 各学院近5年预警趋势
const schoolAlarmTrendData =ref({
})
const getXxAlarmTrends = () => {
  xxAlarmTrends(searchParams.value)
    .then(result => {
      console.log(result,'result各学院近5年预警趋势');
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data各学院近5年预警趋势');
        schoolAlarmTrendData.value = data;
      }
    })
}

//各学院预警学生分布
const schoolClassAlarmData = ref({
   yxScreenVOList: [],
  avgAlarmPersonNum: 0});
const getAlarmDistribution = () => {
  alarmDistribution(searchParams.value)
    .then(result => {
      console.log(result,'result各学院预警学生分布');
      const { data, code } = result.data || {};
      if (code === 200) {
      console.log(data,'data各学院预警学生分布');

         schoolClassAlarmData.value = {
          yxScreenVOList: Array.isArray(data.yxScreenVOList) ? data.yxScreenVOList : [],
          avgAlarmPersonNum: data.avgAlarmPersonNum || 0
        };
      }
    })
     .catch(error => {
      console.error('获取预警分布数据失败:', error);
      // 设置默认空数据避免子组件出错
      schoolClassAlarmData.value = {
        yxScreenVOList: [],
        avgAlarmPersonNum: 0
      };
    });
}

//各学院预警处理情况
const schoolAlarmHandleData = ref({
   yxScreenVOList: [],
  avgAlarmNum: 0,
avgHandleNum:0,});
const getAlarmHandle = () => {
  alarmHandle(searchParams.value)
    .then(result => {
      console.log(result,'各学院预警处理情况');
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data各学院预警处理情况');
         schoolAlarmHandleData.value = {
          yxScreenVOList: Array.isArray(data.yxScreenVOList) ? data.yxScreenVOList : [],
          avgAlarmNum: data.avgAlarmNum || 0,
          avgHandleNum: data.avgHandleNum || 0
        };
      }
    })
    .catch(error => {
      console.error('获取各学院预警处理情况失败:', error);
      // 设置默认空数据避免子组件出错
      schoolAlarmHandleData.value = {
        yxScreenVOList: [],
        avgAlarmNum: 0,
        avgHandleNum:0,
      };
    });
}




</script>

<style lang="scss" scoped>
@use '@/styles/responsive.scss' as *;
.alarm-visualization {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  background:#00062E;
  color: white;
}

.header-main-bg {
  width: 100%;
  height:55vh;
  background-image: url('/img/visualization/headBg.png');
  background-size: 100% 100%;
  box-sizing: border-box;
   .head-title{
    font-family: YouSheBiaoTiHei;
    font-size: vw(34px);
    color: #FFFFFF;
    line-height: vh(58px);
    letter-spacing: 3px;
    text-shadow: 0px 3px 4px #00182E;
    text-align: center;
    font-style: normal;
  }
  .title-selectors{
    display: flex;
    justify-content: space-between;
    .left-title{
      width:vw(330px) ;
      height:vh(52px) ;
      font-family: YouSheBiaoTiHei;
      font-size: vw(26px);
      color: #FFFFFF;
      line-height: 38px;
      text-shadow: 0px 3px 4px #00182E;
      text-align: center;
      font-style: normal;
      background-image: url('/img/visualization/titleBg.png');
      background-size: 100% 100%;
  }
 
  .selectors {
  display: flex;
  gap: 15px;
  margin-right: vw(30px);
}


.custom-select:deep(.el-select__wrapper) {
  width: vw(110px);
  height: vh(30px) !important;
  min-height:30px;
  background: #112F48;
  border-radius: 1px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(128, 192, 253, 0.54), rgba(145, 199, 255, 0.83)) 1 1;
   box-shadow: none;
}

    .custom-select:deep(.el-select__placeholder) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: vw(13px);
      color: #F5F8FF;
      line-height: 17px;
      text-align: left;
      font-style: normal;
    }

.custom-select:deep(.is-transparent) {
  font-family: PingFangSC, PingFang SC;
font-weight: 500;
font-size: vw(13px);
color: #ABD7FF;
line-height: 20px;
text-align: left;
font-style: normal;
}

.custom-select:deep(.el-select__caret) {
    display: none; // 隐藏默认的下拉箭头
  }
  .custom-select:deep(.el-select__suffix) {
    background-image: url('/img/visualization/downImg.png'); // 使用自定义图标
    background-repeat: no-repeat;
    background-position: center;
    background-size: 300% 500%;
    width: vw(10px);
    height: vh(4px);
    box-shadow: 0px 0px 8px 2px #2EB0FF;
  }
 
  }
  
}
.main-content{
  display: flex;
  justify-content: space-between;
 margin-right: vh(30px);
    margin-left: vh(30px);
.card-content{
  width: 100%;
 height: calc(55vh - vh(168px));
background: rgba(9,27,58,0.48);
}
}
  .schoolLevel-card{
    width:32%;
  }
  .schoolLevel-card-big{
    width: 36%;
    margin-right: vh(30px);
    margin-left: vh(30px);

  }
  .schoolLevel-card-title{
   height: vh(48px);
   font-family: 'PangMenZhengDao';
  background-image: url('/img/visualization/subheadingBg.png');
  background-size: 100% 100%;
  font-size: vw(22px);
  color: #FFFFFF;
  line-height: vh(50px);
  text-align: left;
  font-style: normal;
  padding-left: vw(55px);
  margin-bottom: 10px;
}
.title-top{
  font-family: 'YouSheBiaoTiHei';
  .title-top-yellow{
    color: #FFB800;
  }
}
.schoolLevel-card-title-big{
   height: vh(48px);
   font-family: 'PangMenZhengDao';
   background-image: url('/img/visualization/subheadingBg2.png');
  background-size: 100% 100%;
  font-size: vw(22px);
  color: #FFFFFF;
  line-height: vh(48px);
  text-align: left;
  font-style: normal;
  padding-left: vw(55px);
  margin-bottom: 10px;
}
.main-content-1{
  // width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc(45vh - vh(60px)) !important;
  margin: vh(30px) ;
  box-sizing: border-box;
  .card-content1{
      width: 100%;
 height: calc(45vh - vh(118px));
background: rgba(9,27,58,0.48);
  }
}


</style>