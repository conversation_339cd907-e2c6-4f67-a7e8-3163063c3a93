<!-- 各专业预警情况 -->
<template>
    <div class="class-tracking" ref="instituteAlarmSituationRef">
        <el-table :data="tableData" style="width: 100%;background-color: rgba(2, 51, 100, 0.52)"  size="small"
            :row-style="rowStyle" 
            :cell-style="cellStyle" 
            :header-cell-style="headerCellStyle"
             >
            <el-table-column label="专业">
                <template #default="{ row }">
                    <div class="class-name">{{ row.name }}</div>
                </template>
            </el-table-column>
            <el-table-column label="专业课程">
                <template #default="{ row }">
                    <div class="alarm-count">{{ row.alarmCount }}</div>
                </template>
            </el-table-column>
            <el-table-column label="预警课程">
                <template #default="{ row }">
                    <div class="handle-count">{{ row.handleCount }}</div>
                </template>
            </el-table-column>
            <el-table-column label="首修预警率" min-width="100px">
                <template #default="{ row }">
                    <div class="alarm-handle-rate">{{ row.alarmHandleRate }}</div>
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template #default="{ row }">
                    <div class="class-btn">
                        <img src="/img/visualization/viewIcon.png" alt="">
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="cm-pagination-right">
          <el-pagination v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size" 
            layout="prev, pager, next"
            :total="pageParams.total" @change="onLoad" />
        </div>
    </div>
</template>

<script setup>
import { nextTick } from 'vue';
import { useResizeObserver } from '@/hooks/useResizeObserver'
const props = defineProps({
    data: {
        type: Object,
        default: () => [],
    },
})
//分页参数
const pageParams = ref({
  total: 100,
  current: 1,
  size: 10,
})
//加载数据
const onLoad = () => {
  console.log(pageParams.value)
}
const tableData = ref(props.data);
const caculateHeight = () => {
    nextTick(() => {
        const tableHeight = document.querySelector('.class-tracking').offsetHeight - 60 ;
        const rowHeight = Math.ceil(tableHeight/6) ;
        rowStyle.value.height = rowHeight + 'px';
        headerCellStyle.value.height = rowHeight + 'px';
    })
}
const rowStyle = ref({
    background: 'linear-gradient(180deg, rgba(9,27,58,1) 0%,rgba(0,100,249,0) 25%, rgba(0,128,249,0.5) 100%)',
    color: '#ffffff',
})
const cellStyle =ref({
    color: '#ffffff',
    textAlign: 'center',
    padding: '0px',
})
const headerCellStyle = ref({
    background: 'rgba(2, 51, 100, 0.52)',
    color: 'rgba(224,242,255)',
    textAlign: 'center',
})
const instituteAlarmSituationRef = useTemplateRef('instituteAlarmSituationRef');
onMounted(() => {
    useResizeObserver(instituteAlarmSituationRef, entries => {
        caculateHeight();
    })
})
</script>

<style scoped lang="scss">
@use '@/styles/utils.scss' as utils;
.class-tracking {
    :deep(.el-table) {
      --el-table-border-color: transparent;
      --el-table-border: 1px solid transparent;
      --el-table-text-color: #fff;
      --el-table-row-hover-bg-color: transparent;
      --el-table-current-row-bg-color: transparent;
      --el-table-header-bg-color: transparent;
      --el-table-fixed-box-shadow: transparent;
      --el-table-bg-color: transparent;
      --el-table-tr-bg-color: transparent;
    }
    :deep(.el-table__cell) {
      color: rgba(255, 255, 255, 0.84);
    }
    :deep(.el-table--small .cell){
      padding-top: utils.vh(12px);
    }
    :deep(th.el-table__cell) {
      color: rgba(70, 143, 255, 1);
    }
    :deep(.el-pagination){
        --el-pagination-bg-color:none;
        --el-pagination-button-disabled-bg-color: none;
        --el-pagination-button-color:#DCDEE0;
        --el-pagination-hover-color: #FFFFFF;
    }
    :deep(.el-pager li.is-active, .el-pager li:hover) {
        width: 32px;
        height: 32px;
        background: blue;
}
}
.class-name{
    font-size: utils.vw(13px);
}
.alarm-count{
    color: #00FFFF;
    font-size: utils.vw(20px);
}
.handle-count{
    color: #FF7575;
    font-size: utils.vw(20px);
}
.alarm-handle-rate{
    color: #FF7575;
    font-size: utils.vw(20px);
}
.class-btn{
    line-height: 5px;
   img{
    width: utils.vw(68px);
    height:utils.vh(37px);
   }
}
</style>