<!-- 各学院预警率排序 -->
<template>
  <div class="class-alarm" ref="schoolAlarmsortDivRef">
    
    <div ref="schoolAlarmsortRef" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script setup>
import * as echarts from 'echarts';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import sortIcon1 from '/img/visualization/sortIcon1.png' ;
import sortIcon2 from '/img/visualization/sortIcon2.png' ;
import sortIcon3 from '/img/visualization/sortIcon3.png' ;
import sortIcon4 from '/img/visualization/sortIcon4.png' ;
import first from '/img/visualization/first.png' ;
import second from '/img/visualization/second.png' ;
import third from '/img/visualization/third.png' ;

const props = defineProps({
  data: {
    type: Object,
    default: () => ([]),
  },
});
const schoolAlarmsortRef = ref(null);
let classAlarmRefInstance = null;
const schoolAlarmsortDivRef = useTemplateRef('schoolAlarmsortDivRef');
const resizeChart = () => {
  classAlarmRefInstance.resize();
}
const sortData = ref(props.data)
const colorList =ref(['rgba(49,58,70,0.05)','rgba(49,52,70,0.05)','rgba(70,63,49,0.05)','rgba(49,56,70,0.05)'])
const colorList2 =ref(['#FF6A00','#9DA1BA','#D6AD86','#0071A1'])
const imgList =ref([sortIcon1,sortIcon2,sortIcon3,sortIcon4])
console.log(props.data,'props.data');
console.log(sortData.value,'sortData.value');

const getArrByKey = (data, k) => {
    console.log(data,'data');
    
    let key = k || 'value';
    let res = [];
    if (data) {
        data.forEach(function (t) {
            res.push(t[key]);
        });
    }
    console.log('getArrByKey', res);
    return res;
};
const option = {
    // backgroundColor: '#081c47',
   
    tooltip: {
        trigger: 'item',
        show: true,
        formatter: '{b} : <br/>{d}%',
        padding: [8, 10], //内边距
    },
    grid: [
        {
           left: -60,
            right: '0%', // 网格部分
            top: '10%',
            bottom: '0%',
            containLabel: true,
        },
    ],
    xAxis: { 
        show: false ,
    max: 100,
},
    yAxis: [
        {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(sortData.value, 'yxmc'),
            axisLine: { show: false },
            splitLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
                    fontSize: 15,
                    align: 'left',
                    color: '#fff',
                    padding: [0,0,52,-30],
                    interval: 0,
                    rich: {
                        a1: {
                    backgroundColor: {
                    image: first,
                    },
                    width: 28,
                    height: 24,
                        },
                        a2: {
                            backgroundColor: {
                    image: second,
                    },
                    width: 28,
                    height: 24,
                        },
                        a3: {
                             backgroundColor: {
                    image: third,
                    },
                    width: 28,
                    height: 24,
                        },
                        b: {
                            color: '#fff',
                            width: 28,
                            align: 'center',
                            fontSize: 16,
                        }
                    },
                    formatter: function(params) {
                        var index = sortData.value.map(item => item.yxmc).indexOf(params);
                        index = index + 1;
                        if (index - 1 < 3) {
                            return [
                                '{a' + index + '|'  + '}' + '  ' + params
                            ].join('\n')
                        } else {
                            return [
                                '{b|' + index + '}' + '  ' + params
                            ].join('\n')
                        }
                    }
                }
        },
        {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(sortData.value, 'yxmc'),
            axisLine: { show: false },
            splitLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
                interval: 0,
                align: 'right',
                margin: 40,
                lineHeight: 50,
                fontSize: 13,
                color: '#fff',
                formatter: function (value, index) {
                    return (sortData.value[index].alarmRate )*100  + '%';
                },
            },
            
        },
        
    ],
    series: [
      	{
			type: 'pictorialBar',
           data:sortData.value.map((item, i) => {
            console.log(imgList.value[i]);
            
        return {
          value: item.alarmRate,
           symbol: 'image://' + imgList.value[i],// 
        };
      }),
			xAxisIndex: 0,
			yAxisIndex: 0,
			zlevel: 99,
            symbolRotate: 0,
			symbolSize: [15, 20],
			symbolPosition: 'end',
			symbolOffset: [8, -3],
			tooltip: {
				show: false
			},
		},

        {
            name: '条',
            type: 'bar',
            showBackground: true,
           data: sortData.value.map((item, i) => {
        return {
          value: item.alarmRate,
          itemStyle: {
            normal:{
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
            {
                offset: 0,
                color: colorList.value[i],
            },
            {
                offset: 1,
                color: colorList2.value[i],
            },
            ]),
            }
            
          },
        };
      }),
            barWidth: 8,
        },
    ],
};
onMounted(() => {
  console.log(props.data);
  
  useResizeObserver(schoolAlarmsortDivRef, entries => {
    resizeChart();
  })
  nextTick(() => {
  // 初始化图表
    classAlarmRefInstance = echarts.init(schoolAlarmsortRef.value);
    classAlarmRefInstance.setOption(option);
  })
});
</script>
<style lang="scss" scoped>
</style>