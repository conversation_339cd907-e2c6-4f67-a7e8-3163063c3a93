<template>
    <div class="class-tracking" ref="classTrackingRef">
        <el-table :data="tableData" style="width: 100%;background-color: rgba(2, 51, 100, 0.52)"  size="small"
            :row-style="rowStyle" 
            :cell-style="cellStyle" 
            :header-cell-style="headerCellStyle" >
            <el-table-column label="专业">
                <template #default="{ row }">
                    <div class="class-name">{{ row.name }}</div>
                </template>
            </el-table-column>
            <el-table-column label="班级">
                <template #default="{ row }">
                    <div class="class-name">{{ row.className }}</div>
                </template>
            </el-table-column>
            <el-table-column label="专业课程">
                <template #default="{ row }">
                    <div class="alarm-count">{{ row.alarmCount }}</div>
                </template>
            </el-table-column>
            <el-table-column label="预警课程">
                <template #default="{ row }">
                    <div class="handle-count">{{ row.handleCount }}</div>
                </template>
            </el-table-column>
            <el-table-column label="首修预警率" min-width="100px">
                <template #default="{ row }">
                    <div class="alarm-handle-rate">{{ row.alarmHandleRate }}</div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
import { nextTick } from 'vue';
import { useResizeObserver } from '@/hooks/useResizeObserver'
const props = defineProps({
    data: {
        type: Object,
        default: () => [],
    },
})
const tableData = ref(props.data);
const caculateHeight = () => {
    nextTick(() => {
        const tableHeight = document.querySelector('.class-tracking').offsetHeight;
        const rowHeight = Math.ceil(tableHeight/6);
        rowStyle.value.height = rowHeight + 'px';
        headerCellStyle.value.height = rowHeight + 'px';
    })
}
const rowStyle = ref({
    background: 'linear-gradient(180deg, rgba(0,100,249,0) 0%, rgba(0,128,249,0.5) 100%)',
    color: '#ffffff',
})
const cellStyle =ref({
    color: '#ffffff',
    textAlign: 'center',
    padding: '0px',
})
const headerCellStyle = ref({
    background: 'rgba(2, 51, 100, 0.52)',
    color: 'rgba(224,242,255)',
    textAlign: 'center',
})
const classTrackingRef = useTemplateRef('classTrackingRef');
onMounted(() => {
    useResizeObserver(classTrackingRef, entries => {
        caculateHeight();
    })
})
</script>

<style scoped lang="scss">
@use '@/styles/utils.scss' as utils;
.class-tracking {
    :deep(.el-table) {
      --el-table-border-color: transparent;
      --el-table-border: 1px solid transparent;
      --el-table-text-color: #fff;
      --el-table-row-hover-bg-color: transparent;
      --el-table-current-row-bg-color: transparent;
      --el-table-header-bg-color: transparent;
      --el-table-fixed-box-shadow: transparent;
      --el-table-bg-color: transparent;
      --el-table-tr-bg-color: transparent;
    }
    :deep(.el-table__cell) {
      color: rgba(255, 255, 255, 0.84);
    }
    :deep(th.el-table__cell) {
      color: rgba(70, 143, 255, 1);
    }
}
.class-name{
    font-size: utils.vw(13px);
}
.alarm-count{
    color: #00FFFF;
    font-size: utils.vw(20px);
}
.handle-count{
    color: #FF7575;
    font-size: utils.vw(20px);
}
.alarm-handle-rate{
    color: #FF7575;
    font-size: utils.vw(20px);
}
</style>