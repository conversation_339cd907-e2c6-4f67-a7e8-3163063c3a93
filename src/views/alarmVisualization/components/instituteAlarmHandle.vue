<!-- 各专业预警处理情况 -->
<template>
  <div class="alarm-handle" ref="instituteAlarmHandleDivRef">
    <div class="alarm-handle-title">
      <div class="alarm-handle-title-item">
        <img src="/img/largeScreen/major-lengend1.png"   alt="">
        <span>预警次数</span>
      </div>
      <div class="alarm-handle-title-item">
        <img src="/img/largeScreen/major-lengend2.png"   alt="">
        <span>处理次数</span>
      </div>
      <div class="alarm-handle-title-item"> 
        <div class="alarm-handle-title-line" style="background-color: #FDFB50;"></div>
        <span>专业平均预警次数</span>
      </div>
      <div class="alarm-handle-title-item"> 
        <div class="alarm-handle-title-line" style="background-color: #30FF91;"></div>
        <span>专业平均处理次数</span>
      </div>
    </div>
    <div ref="instituteClassAlarmRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { useResizeObserver } from '@/hooks/useResizeObserver'
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})
// 格式化x轴标签文本
const formatXAxisLabel = (text) => {
  if (!text) return '';
  // 如果文本过长，进行换行处理
  if (text.length > 6) {
    return text.substring(0, 6) + '\n' + text.substring(6);
  }
  return text;
};

const offsetX = 12;
const offsetY = 6;
// 绘制左侧面
const CubeLeft = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    // 会canvas的应该都能看得懂，shape是从custom传入的
    const xAxisPoint = shape.xAxisPoint;
    // console.log(shape);
    const c0 = [shape.x, shape.y];
    const c1 = [shape.x - offsetX, shape.y - offsetY];
    const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
    const c3 = [xAxisPoint[0], xAxisPoint[1]];
    ctx
      .moveTo(c0[0], c0[1])
      .lineTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .closePath();
  },
});
// 绘制右侧面
const CubeRight = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c1 = [shape.x, shape.y];
    const c2 = [xAxisPoint[0], xAxisPoint[1]];
    const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
    const c4 = [shape.x + offsetX, shape.y - offsetY];
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
// 绘制顶面
const CubeTop = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const c1 = [shape.x, shape.y];
    const c2 = [shape.x + offsetX, shape.y - offsetY]; // 右点
    const c3 = [shape.x, shape.y - offsetX];
    const c4 = [shape.x - offsetX, shape.y - offsetY];
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
// 注册三个面图形
echarts.graphic.registerShape('CubeLeft', CubeLeft);
echarts.graphic.registerShape('CubeRight', CubeRight);
echarts.graphic.registerShape('CubeTop', CubeTop);



const option = {
  //你的代码
  // backgroundColor: '#031a40',
//   legend: {
//     show: true,
//     right: 30,
//     top: 10,
//     itemGap: 30,
//     itemWidth: 20,
//     itemHeight: 10,
//     data: ['时长', '占比'],
//     textStyle: {
//       fontSize: 18,
//       color: '#ffffff',
//     },
//   },
  color: ['#E7D36B', '#26D6D7', '#43EFFF'],
  tooltip: {
    trigger: 'axis',
    textStyle: {
      fontSize: 12, // 字体大小
    },
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '3%',
    bottom: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [2, 3, 4, 5, 6, 7],
    axisLine: {
      show: true,
      lineStyle: {
        width: 1,
        color: 'rgba(239, 247, 253, .1)',
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      fontSize: 12,
      color: '#E7FCFF',
      margin: 10,
       // 处理标签过长问题
        formatter: function(value) {
          return formatXAxisLabel(value);
        },
        // 旋转标签以节省空间
        rotate: 0,
        // 设置标签间隔
        interval: 0,
        // 允许换行
        lineHeight: 14
    },
  },
  yAxis: [
    {
      type: 'value',
      // name: '时长：天',
      nameGap: 30,
      nameTextStyle: {
        color: '#ffffff',
        fontWeight: 400,
        fontSize: 16,
      },
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 16,
        color: '#E7FCFF',
      },
      min: 0,
      max: 10,
      interval: 1,
    },

  ],
   dataZoom: [
        {
            show: true,
            height: 1,
            xAxisIndex: [0],
            bottom: 10,
            showDetail: false,
            borderColor: 'transparent',
            textStyle: {
                fontSize: 0,
            },
            endValue: 4,//从0开始的相当于5个
            backgroundColor: 'rgba(0,0,0,0)',
            borderWidth: 0,
            handleSize: '0%',
            handleStyle: {
                color: '#d3dee5',
            },
        },
    ],
  series: [
    {
      name: '预警次数',
      type: 'custom',
      color: '#3666E0',
      renderItem: (params, api) => {
        const location = api.coord([api.value(0), api.value(1)]);
        return {
          type: 'group',
          x: -15,
          children: [
            {
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(0, 20, 167, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 57, 138, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(129, 150, 242, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(102, 97, 254, 0.12)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(34, 90, 232, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(34, 90, 232, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
          ],
        };
      },
      data: [],
    },
    {
      name: '处理次数',
      type: 'custom',
      color:'#75D5D7',
      renderItem: (params, api) => {
        const location = api.coord([api.value(0), api.value(1)]);
        return {
          type: 'group',
          x: 15,
          children: [
            {
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(0, 152, 179, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 125, 155, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(29, 231, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 130, 166, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(114, 255, 255, .9)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(114, 255, 255, .9)',
                  },
                ]),
                stroke: 'rgba(114, 255, 255, .7)', //边框颜色
              },
            },
          ],
        };
      },
      data: [],
    },
    {
      name: '专业平均预警次数',
      type: 'line',
      color: '#FDFB50',
      symbol: "line",
      // data: [150, 150, 150, 150],
      markLine: {
        data: [{
          type: 'average',
          name: '平均值',
          yAxis: 150,
          label: {
            show: false
          }
        }],
        symbol: 'none',
        lineStyle: {
          color: '#FDFB50',
          type: 'solid',
          width: 2,
          shadowColor: 'rgba(177, 255, 206, 0.5)',
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
      }
    },
    {
      name: '专业平均处理次数',
      type: 'line',
      color: '#30FF91',
      symbol: "line",
      // data: [200, 200, 200, 200],
      markLine: {
        data: [{
          type: 'average',
          name: '平均值',
          yAxis: 200,
          label: {
            show: false
          }
        }],
        symbol: 'none',
        lineStyle: {
          color: '#30FF91',
          type: 'solid',
          width: 2,
          shadowColor: 'rgba(177, 255, 206, 0.5)',
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
      }
    },
  ],
};
const instituteClassAlarmRef = ref(null);
let alarmHandleRefInstance = null;
const instituteAlarmHandleDivRef = useTemplateRef('instituteAlarmHandleDivRef');
const getArrByKey = (data, k) => {
    let key = k || 'value';
    let res = [];
    if (data) {
        data.forEach(function (t) {
            res.push(t[key]);
        });
    }
    return res;
};
const initChart = (data) => {
  const avgAlarmNum = data.avgAlarmNum;
  const avgHandleNum = data.avgHandleNum;
  
  const yxScreenVOList = data.yxScreenVOList;
  // 数据验证
  if (!yxScreenVOList || yxScreenVOList.length === 0) {
    return;
  }
    // 设置基础数据
  option.xAxis.data = getArrByKey(yxScreenVOList, 'yxmc');
  option.series[0].data = getArrByKey(yxScreenVOList, 'alarmNum'); // 预警次数
  option.series[1].data =getArrByKey(yxScreenVOList, 'handleNum'); // 处理次数
  
  // 计算并设置预警次数平均值
  option.series[2].markLine.data[0].yAxis = avgAlarmNum;
  option.series[2].data = getArrByKey(yxScreenVOList, 'alarmNum').map(() => avgAlarmNum);
  
  // 计算并设置处理次数平均值
  option.series[3].markLine.data[0].yAxis = avgHandleNum;
  option.series[3].data = getArrByKey(yxScreenVOList, 'handleNum').map(() => avgHandleNum);
  
  // 配置 Y 轴
  const maxValue = Math.max(...getArrByKey(yxScreenVOList, 'alarmNum'));
  option.yAxis[0].max = Math.ceil(maxValue / 5) * 6;
  option.yAxis[0].interval = Math.ceil(maxValue / 5);
  
  nextTick(() => {
    if (!alarmHandleRefInstance) {
      alarmHandleRefInstance = echarts.init(instituteClassAlarmRef.value);
    }
    alarmHandleRefInstance.setOption(option, true); // 使用 true 参数替换旧配置
  });
};
watch(() => props.data, (newData) => {
  if (newData && Object.keys(newData).length > 0 && newData.yxScreenVOList) {
    initChart(newData);
  }
}, { immediate: true, deep: true });


onMounted(() => {
  
  useResizeObserver(instituteAlarmHandleDivRef, entries => {
      if (alarmHandleRefInstance) {
      alarmHandleRefInstance.resize();
    }
    if (props.data && Object.keys(props.data).length > 0 && props.data.yxScreenVOList) {
    initChart(props.data);
  }
  })

});

</script>

<style scoped lang="scss">
@use '@/styles/utils.scss' as utils;
.alarm-handle {
  // width: utils.vw(565px);
  // height: utils.vh(400px);
  position: relative;
  .alarm-handle-title {
    position: absolute;
    z-index: 99;
    top: utils.vw(20px);
    right: utils.vw(10px);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: utils.vw(10px);
    .alarm-handle-title-item {
      display: flex;
      align-items: center;
      gap: utils.vw(5px);
      font-size: utils.vw(12px);
      color: #fff;
      .alarm-handle-title-line {
        width: utils.vw(15px);
        height: utils.vh(3px);
      }
    }
  }
}

</style>