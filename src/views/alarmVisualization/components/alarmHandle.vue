<template>
  <div class="alarm-handle" ref="alarmHandleDivRef">
    <div class="alarm-handle-title">
      <div class="alarm-handle-title-item">
        <img src="/img/largeScreen/major-lengend1.png"   alt="">
        <span>预警次数</span>
      </div>
      <div class="alarm-handle-title-item">
        <img src="/img/largeScreen/major-lengend2.png"   alt="">
        <span>处理次数</span>
      </div>
      <div class="alarm-handle-title-item"> 
        <div class="alarm-handle-title-line" style="background-color: #FDFB50;"></div>
        <span>班级平均预警次数</span>
      </div>
      <div class="alarm-handle-title-item"> 
        <div class="alarm-handle-title-line" style="background-color: #30FF91;"></div>
        <span>学院平均处理次数</span>
      </div>
    </div>
    <div ref="alarmHandleRef" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { useResizeObserver } from '@/hooks/useResizeObserver'
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const offsetX = 12;
const offsetY = 6;
// 绘制左侧面
const CubeLeft = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    // 会canvas的应该都能看得懂，shape是从custom传入的
    const xAxisPoint = shape.xAxisPoint;
    // console.log(shape);
    const c0 = [shape.x, shape.y];
    const c1 = [shape.x - offsetX, shape.y - offsetY];
    const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
    const c3 = [xAxisPoint[0], xAxisPoint[1]];
    ctx
      .moveTo(c0[0], c0[1])
      .lineTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .closePath();
  },
});
// 绘制右侧面
const CubeRight = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const xAxisPoint = shape.xAxisPoint;
    const c1 = [shape.x, shape.y];
    const c2 = [xAxisPoint[0], xAxisPoint[1]];
    const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
    const c4 = [shape.x + offsetX, shape.y - offsetY];
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
// 绘制顶面
const CubeTop = echarts.graphic.extendShape({
  shape: {
    x: 0,
    y: 0,
  },
  buildPath: function (ctx, shape) {
    const c1 = [shape.x, shape.y];
    const c2 = [shape.x + offsetX, shape.y - offsetY]; // 右点
    const c3 = [shape.x, shape.y - offsetX];
    const c4 = [shape.x - offsetX, shape.y - offsetY];
    ctx
      .moveTo(c1[0], c1[1])
      .lineTo(c2[0], c2[1])
      .lineTo(c3[0], c3[1])
      .lineTo(c4[0], c4[1])
      .closePath();
  },
});
// 注册三个面图形
echarts.graphic.registerShape('CubeLeft', CubeLeft);
echarts.graphic.registerShape('CubeRight', CubeRight);
echarts.graphic.registerShape('CubeTop', CubeTop);



const option = {
  //你的代码
  backgroundColor: '#031a40',
//   legend: {
//     show: true,
//     right: 30,
//     top: 10,
//     itemGap: 30,
//     itemWidth: 20,
//     itemHeight: 10,
//     data: ['时长', '占比'],
//     textStyle: {
//       fontSize: 18,
//       color: '#ffffff',
//     },
//   },
  color: ['#E7D36B', '#26D6D7', '#43EFFF'],
  tooltip: {
    trigger: 'axis',
    textStyle: {
      fontSize: 12, // 字体大小
    },
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '3%',
    bottom: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [2, 3, 4, 5, 6, 7],
    axisLine: {
      show: true,
      lineStyle: {
        width: 1,
        color: 'rgba(239, 247, 253, .1)',
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      fontSize: 12,
      color: '#E7FCFF',
      margin: 10,
    },
  },
  yAxis: [
    {
      type: 'value',
      // name: '时长：天',
      nameGap: 30,
      nameTextStyle: {
        color: '#ffffff',
        fontWeight: 400,
        fontSize: 16,
      },
      axisLine: {
        show: true,
        lineStyle: {
          width: 1,
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(239, 247, 253, .1)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 16,
        color: '#E7FCFF',
      },
      min: 0,
      max: 10,
      interval: 1,
    },

  ],
  series: [
    {
      name: '预警次数',
      type: 'custom',
      color: '#3666E0',
      renderItem: (params, api) => {
        const location = api.coord([api.value(0), api.value(1)]);
        return {
          type: 'group',
          x: -15,
          children: [
            {
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(0, 20, 167, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 57, 138, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(129, 150, 242, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(102, 97, 254, 0.12)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(34, 90, 232, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(34, 90, 232, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
          ],
        };
      },
      data: [],
    },
    {
      name: '处理次数',
      type: 'custom',
      color:'#75D5D7',
      renderItem: (params, api) => {
        const location = api.coord([api.value(0), api.value(1)]);
        return {
          type: 'group',
          x: 15,
          children: [
            {
              type: 'CubeLeft',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(0, 152, 179, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 125, 155, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeRight',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(29, 231, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 130, 166, 1)',
                  },
                ]),
                stroke: 'rgba(129, 150, 242, .7)', //边框颜色
              },
            },
            {
              type: 'CubeTop',
              shape: {
                api,
                xValue: api.value(0),
                yValue: api.value(1),
                x: location[0],
                y: location[1],
                xAxisPoint: api.coord([api.value(0), 0]),
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(114, 255, 255, .9)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(114, 255, 255, .9)',
                  },
                ]),
                stroke: 'rgba(114, 255, 255, .7)', //边框颜色
              },
            },
          ],
        };
      },
      data: [],
    },
    {
      name: '班级平均预警次数',
      type: 'line',
      color: '#FDFB50',
      symbol: "line",
      // data: [150, 150, 150, 150],
      markLine: {
        data: [{
          type: 'average',
          name: '平均值',
          yAxis: 150,
          label: {
            show: false
          }
        }],
        symbol: 'none',
        lineStyle: {
          color: '#FDFB50',
          type: 'solid',
          width: 2,
          shadowColor: 'rgba(177, 255, 206, 0.5)',
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
      }
    },
    {
      name: '学院平均处理次数',
      type: 'line',
      color: '#30FF91',
      symbol: "line",
      // data: [200, 200, 200, 200],
      markLine: {
        data: [{
          type: 'average',
          name: '平均值',
          yAxis: 200,
          label: {
            show: false
          }
        }],
        symbol: 'none',
        lineStyle: {
          color: '#30FF91',
          type: 'solid',
          width: 2,
          shadowColor: 'rgba(177, 255, 206, 0.5)',
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
      }
    },
  ],
};
const alarmHandleRef = ref(null);
let alarmHandleRefInstance = null;
const alarmHandleDivRef = useTemplateRef('alarmHandleDivRef');
const resizeChart = () => {
  alarmHandleRefInstance.resize();  
}
// 计算数组平均值的辅助函数
const calculateAverage = (data) => {
  const sum = data.reduce((acc, current) => acc + current, 0);
  return Math.ceil(sum / data.length);
};

onMounted(() => {
  // console.log(props.data);
  
  const { xAxisData, seriesData1, seriesData2 } = props.data;
  
  // 设置基础数据
  option.xAxis.data = xAxisData;
  option.series[0].data = seriesData1; // 预警次数
  option.series[1].data = seriesData2; // 处理次数
  
  // 计算并设置预警次数平均值
  const warningAverage = calculateAverage(seriesData1);
  option.series[2].markLine.data[0].yAxis = warningAverage;
  option.series[2].data = seriesData1.map(() => warningAverage);
  
  // 计算并设置处理次数平均值
  const handleAverage = calculateAverage(seriesData2);
  option.series[3].markLine.data[0].yAxis = handleAverage;
  option.series[3].data = seriesData2.map(() => handleAverage);
  
  // 配置 Y 轴
  const maxValue = Math.max(...seriesData1);
  option.yAxis[0].max = Math.ceil(maxValue / 5) * 6;
  option.yAxis[0].interval = Math.ceil(maxValue / 5);
  
  useResizeObserver(alarmHandleDivRef, entries => {
    resizeChart();
  })
  // 初始化图表
  nextTick(() => {
    alarmHandleRefInstance = echarts.init(alarmHandleRef.value);
    alarmHandleRefInstance.setOption(option);
  });
});
</script>

<style scoped lang="scss">
@use '@/styles/utils.scss' as utils;
.alarm-handle {
  // width: utils.vw(565px);
  // height: utils.vh(400px);
  position: relative;
  .alarm-handle-title {
    position: absolute;
    z-index: 99;
    top: utils.vw(20px);
    right: utils.vw(10px);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: utils.vw(10px);
    .alarm-handle-title-item {
      display: flex;
      align-items: center;
      gap: utils.vw(5px);
      font-size: utils.vw(12px);
      color: #fff;
      .alarm-handle-title-line {
        width: utils.vw(15px);
        height: utils.vh(3px);
      }
    }
  }
}

</style>