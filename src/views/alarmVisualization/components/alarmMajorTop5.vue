<!-- 学院预警专业 TOP5 /  重点预警专业 TOP5-->
<template>
  <div class="alarm-Major-Top5" ref="alarmMajorTop5DivRef">
    <!-- <div class="imgList" >
        <div>
            <img src="/img/visualization/top1.png" alt="">
        </div>
         <div>
            <img src="/img/visualization/top2.png" alt="">
        </div>
         <div>
            <img src="/img/visualization/top3.png" alt="">
        </div>
        <div>
           04
        </div>
        <div>
            05
        </div>
    </div> -->
    <div ref="alarmMajorTop5Ref" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script setup>
import * as echarts from 'echarts';
import { useResizeObserver } from '@/hooks/useResizeObserver';
import top5Icon from '/img/visualization/top5Icon.png' ;
import top1 from '/img/visualization/top1.png' ;
import top2 from '/img/visualization/top2.png' ;
import top3 from '/img/visualization/top3.png' ;

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const alarmMajorTop5Ref = ref(null);
let classAlarmRefInstance = null;
const alarmMajorTop5DivRef = useTemplateRef('alarmMajorTop5DivRef');
const resizeChart = () => {
  classAlarmRefInstance.resize();
}
const sortData = ref(props.data)
const colorList =ref(['rgba(255,159,0,0)','rgba(0,228,255,0)','rgba(0,255,163,0)','rgba(0,88,255,0)','rgba(0,88,255,0'])
const colorList2 =ref(['#FDF1CD','#CDFDFC','#CDFDF4','#CDE7FD','#CDE7FD'])

const maxNameLength = computed(() => {
  if (!sortData.value || sortData.value.length === 0) return 0;
  
  // 计算所有名称的字符长度
  const lengths = sortData.value.map(item => item.name.length);
  console.log(lengths,'lengths');
  
  return Math.max(...lengths);
});

// 计算 name 的宽度（根据字符长度动态调整）
const nameWidth = computed(() => {
  // 每个中文字符大约需要 16px 宽度，加上一些间距
  console.log('maxNameLength.value',maxNameLength.value);
  console.log(maxNameLength.value * 14);
  
  return maxNameLength.value * 16 +30; // 20px 为额外间距
});
const getArrByKey = (data, k) => {
    let key = k || 'value';
    let res = [];
    if (data) {
        data.forEach(function (t) {
            res.push(t[key]);
        });
    }
    return res;
};
const option = {
    // backgroundColor: '#081c47',
   
    tooltip: {
        trigger: 'item',
        show: true,
        formatter: '{b} : <br/>{d}%',
        padding: [8, 10], //内边距
    },
    grid: [
        {
           left: '3%',
            right: '0%', // 网格部分
            top: '10%',
            bottom: '0%',
            containLabel: true,
        },
    ],
    xAxis: { 
        show: false ,
    max: 100,
},
    yAxis: [
        {
        type: 'category',
        inverse: true,
        axisLabel: {
            show: true,
                    fontSize: 15,
                    align: 'left',
                    color: '#fff',
                    offset: 100,
                    padding: [0,0,0,-nameWidth.value],
                    interval: 0,
                    height: 24,
                    lineHeight: 24,
                    rich: {
                        a1: {
                    backgroundColor: {
                    image: top1,
                    },
                    width: 28,
                    height: 24,
                    align:'center',
                        },
                        a2: {
                            backgroundColor: {
                    image: top2,
                    },
                    width: 28,
                    height: 24,
                    align:'center',
                        },
                        a3: {
                             backgroundColor: {
                    image: top3,
                    },
                    width: 28,
                    height: 24,
                    align:'center',
                        },
                        b: {
                            color: '#fff',
                            width: 28,
                            align: 'center',
                            fontSize: 16,
                        },
                    //     c: {
                    //     color: '#fff',
                    //     fontSize: 15,
                    //     align: 'left',
                    //     padding: [0, 0, 0, 10],
                    // },
                    name:{
                        width: nameWidth.value-30,
                        fontSize: 15,
                        align: 'right',
                        height: 24,
                        color: '#D8F0FF',
                        lineHeight: 24,
                    }
                    },
                    formatter: function(params) {
                        var index = sortData.value.map(item => item.name).indexOf(params);
                        index = index + 1;
                        if (index - 1 < 3) {
                            return [
                                '{a' + index + '|'  + '}'  + '{name|' +params + '}'
                            ].join('\n')
                        } else {
                            return [
                                '{b|' +'0'+ index + '}'  +'{name|'+ params + '}'
                            ].join('\n')
                        }
                    }
                
        },
        splitLine: {
            show: false
        },
        axisTick: {
            show: false
        },
        axisLine: {
            show: true,
            lineStyle: {
          width: 1,
          color: 'rgba(239, 247, 253, .1)',
        },
        },
        data: getArrByKey(sortData.value, 'name'),
    }, 
    
        {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: getArrByKey(sortData.value, 'name'),
            axisLine: { show: false },
            splitLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
                interval: 0,
                align: 'right',
                margin: 40,
                lineHeight: 50,
                fontSize: 13,
                color: '#fff',
                formatter: function (value, index) {
                    return (sortData.value[index].value )  + '%';
                },
            },
            
        },
        
    ],
    series: [
      	{
			type: 'pictorialBar',
			data:sortData.value,
			xAxisIndex: 0,
			yAxisIndex: 0,
			zlevel: 0,
            symbol: 'image://' + top5Icon,// 
            symbolRotate: 0,
			symbolSize: [6, 18],
			symbolPosition: 'end',
			symbolOffset: [6, 0],
			tooltip: {
				show: false
			},
		},

        {
            name: '条',
            type: 'bar',
            showBackground: true,
           data: sortData.value.map((item, i) => {
        return {
          value: item.value,
          itemStyle: {
            normal:{
                color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
            {
                offset: 0,
                color: colorList.value[i],
            },
            {
                offset: 1,
                color: colorList2.value[i],
            },
            ]),
            }
            
          },
        };
      }),
            barWidth: 8,
        },
    ],
};
onMounted(() => {
  console.log(props.data);
  
  useResizeObserver(alarmMajorTop5DivRef, entries => {
    resizeChart();
  })
  nextTick(() => {
  // 初始化图表
    classAlarmRefInstance = echarts.init(alarmMajorTop5Ref.value);
    classAlarmRefInstance.setOption(option);
  })
});
</script>
<style lang="scss" scoped>
@use '@/styles/utils.scss' as utils;
.alarm-Major-Top5{
    display: flex;
    justify-content: space-between;
    position: relative;
    .imgList{
        
        position: absolute;
        top: utils.vh(52px);
        left: utils.vw(20px);
        bottom: utils.vh(45px);
        right: utils.vw(10px);;
         display: flex;
         flex-direction: column;
         justify-content: space-between;
         div{ 
            width: utils.vw(28px);
             height: utils.vh(24px);
             text-align: center;
              img{
             width: 100%;
            height:100%;
        }
         }
       
       
    }
}
</style>