<!-- 院级预警可视化 -->
<template>
  <div class="alarm-visualization">
    <!-- 顶部标题和选择器 -->
    <div class="header-main-bg">
      <div class="head-title">学情预警可视化</div>
      <div class="title-selectors">
         <div class="left-title">
          <img src="/img/visualization/back.png" alt="">
        <span class="text1">返回</span>
        <span class="text2"></span>
        <span class="text3">通信工程学院</span>
      </div>
      <div class="selectors">
        <div class="selector-item">
          <el-select v-model="searchParams.xn" placeholder="请选择年份" class="custom-select">
            <el-option label="2025年" value="2025"></el-option>
            <el-option label="2024年" value="2024"></el-option>
            <el-option label="2023年" value="2023"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="searchParams.xq" placeholder="请选择学期" class="custom-select">
            <el-option label="第一学期" value="1"></el-option>
            <el-option label="第二学期" value="2"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="searchParams.nj" placeholder="请选择年级" class="custom-select">
              <el-option
            v-for="item in njList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
          </el-select>
        </div>
      </div>
      </div>
    <!-- 主体内容 -->
     <div class="main-content">
       <!-- 各专业预警学生分布 -->
        <div class="schoolLevel-card">
          <div class="schoolLevel-card-title">各专业预警学生分布</div>
          <div class="card-content">
             <instituteClassAlarm :data="instituteClassAlarmData" ref="instituteClassAlarmRef" style="width: 100%; height: 100%;"/>
          </div>
        </div>
          
          
       
        <!-- 全院预警概况 -->
        <div class="schoolLevel-card-big ">
          <div class="schoolLevel-card-title-big">全院预警概况</div>
          <div class="card-content">
             <schoolAlarm />
          </div>
        </div>

        <!-- 各专业预警情况 -->
        <div class="schoolLevel-card ">
          <div class="schoolLevel-card-title">各专业预警情况</div>
          <div class="card-content">
             <instituteAlarmSituation :data="instituteAlarmSituationData" ref="instituteAlarmSituationRef" style="width: 100%; height: 100%;" />
          </div>
         
        </div>
     </div>
       
     </div>
      <!-- 主体内容 -->
       <div class=" main-content-1">
        <!-- 各专业预警处理情况 -->
        <div class="schoolLevel-card handling-situation">
          <div class="schoolLevel-card-title">各专业预警处理情况</div>
          <div class="card-content1">
             <instituteAlarmHandle :data="instituteAlarmHandleData" ref="instituteAlarmHandleRef" style="width: 100%; height: 100%;" />
          </div>
         
        </div>
        <!-- 预警专业热门度 -->
        <div class="schoolLevel-card-big ranking">
          <div class="schoolLevel-card-title">预警专业热门度</div>
          <div class="card-content1">
             <instituteAlarmMajorPopularity :data="instituteAlarmMajorPopularityData" ref="instituteAlarmMajorPopularityRef" style="width: 100%; height: 100%;" />

            
          </div>
        </div>
        <!-- 重点预警专业 TOP5 -->
        <div class="schoolLevel-card top5-profession">
          <div class="schoolLevel-card-title">重点预警专业 <span class="title-top">TOP<span class="title-top-yellow">5</span></span></div>
          <div class="card-content1">
            <alarmMajorTop5 :data="alarmMajorTop5Data" ref="alarmMajorTop5Ref" style="width: 100%; height: 100%;" />
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, onMounted ,onUnmounted} from 'vue';
import * as echarts from 'echarts';
import instituteAlarmHandle from './components/instituteAlarmHandle.vue'
import instituteClassAlarm from './components/instituteClassAlarm.vue'
import schoolAlarm from './components/schoolAlarm.vue'
import instituteAlarmSituation from './components/instituteAlarmSituation.vue'
import instituteAlarmMajorPopularity from './components/instituteAlarmMajorPopularity.vue'
import alarmMajorTop5 from './components/alarmMajorTop5.vue'
import {yxAlarmDistribution,alarmZyHandle,yxAlarmZyRank,alarmZy,comData} from '@/api/alarmVisualization';
import useBusinessData from '@/hooks/useBusinessData';
const { njList, } = useBusinessData('nj', );
const searchParams = ref({
  nj: '',
  xn: '',
  xq: '1',
});

// 初始化图表
onMounted(() => {
 getAlarmZy()
 getAlarmZyHandle()
 getYxAlarmDistribution()
});
//重点预警专业 TOP5
const alarmMajorTop5Data =ref(
  [
    {
        name: '人工智能',
        value: 50,
    },
    {
        name: '物联网吃付工程',
        value: 20,
    },
    {
        name: '现代物业管理',
        value: 70,
    },
    {
        name: '软件技术',
        value: 40,
    },
    {
        name: '计算机网络',
        value: 43,
    }
  ]
)
//各专业预警学生分布
const instituteClassAlarmData = ref({
  seriesData1: [10, 20, 30, 40, 50, 70],
  xAxisData: ['2024', '2025', '2026', '2027', '2028', '2029'],
});
const getYxAlarmDistribution = () => {
  yxAlarmDistribution(searchParams.value)
    .then(result => {
      console.log(result,'result各专业预警学生分布');
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data各专业预警学生分布');
         instituteClassAlarmData.value = {
          yxScreenVOList: Array.isArray(data.yxScreenVOList) ? data.yxScreenVOList : [],
          avgAlarmPersonNum: data.avgAlarmPersonNum || 0
        };
      }
    })
     .catch(error => {
      console.error('获取预警分布数据失败:', error);
      // 设置默认空数据避免子组件出错
      instituteClassAlarmData.value = {
        yxScreenVOList: [],
        avgAlarmPersonNum: 0
      };
    });
}
//各专业预警处理情况
const instituteAlarmHandleData = ref({
  seriesData1: [10, 20, 30, 40, 50, 70],
  seriesData2: [20, 30, 30, 10, 50, 60],
  xAxisData: ['2024', '2025', '2026', '2027', '2028', '2029'],
});
const getAlarmZyHandle = () => {
  alarmZyHandle(searchParams.value)
    .then(result => {
      console.log(result,'result各专业预警处理情况');
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data各专业预警处理情况');
        instituteAlarmHandleData.value = {
          yxScreenVOList: Array.isArray(data.yxScreenVOList) ? data.yxScreenVOList : [],
          avgAlarmNum: data.avgAlarmNum || 0,
          avgHandleNum: data.avgHandleNum || 0
        };
      }
    })
    .catch(error => {
      console.error('获取各学院预警处理情况失败:', error);
      // 设置默认空数据避免子组件出错
      instituteAlarmHandleData.value = {
        yxScreenVOList: [],
        avgAlarmNum: 0,
        avgHandleNum:0,
      };
    });
}
//预警专业热门度
const instituteAlarmMajorPopularityData =ref(
 [{
        name: "雨伞",
        value: 30
    },
    {
        name: "晴天",
        value: 28
    },
    {
        name: "电话",
        value: 24
    },
    {
        name: "手机",
        value: 23
    },
    {
        name: "下雨",
        value: 22
    },
    {
        name: "暴雨",
        value: 21
    },
    {
        name: "多云",
        value: 20
    },
    {
        name: "雨衣",
        value: 29
    },
    {
        name: "屋檐",
        value: 28
    },
    {
        name: "大风",
        value: 27
    },
    {
        name: "台风",
        value: 26
    },
    {
        name: "下雪",
        value: 25
    },
    {
        name: "打雷",
        value: 24
    },
    {
        name: "小雨",
        value: 30
    },
    {
        name: "中雨",
        value: 18
    },
    {
        name: "大雨",
        value: 14
    },
    {
        name: "雷阵雨",
        value: 13
    },
    {
        name: "下雪",
        value: 12
    },
    {
        name: "小雪",
        value: 11
    },
    {
        name: "中雪",
        value: 10
    },
    {
        name: "大雪",
        value: 9
    },
    {
        name: "暴雪",
        value: 8
    },
    {
        name: "东风",
        value: 7
    },
    {
        name: "南风",
        value: 6
    },
    {
        name: "西北风",
        value: 5
    },
    {
        name: "北风",
        value: 4
    },
    {
        name: "闪电",
        value: 3
    }
]
);
//各专业预警情况
const instituteAlarmSituationData = ref([
    {
        name: '专业1',
        className: '班级1',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业2',
        className: '班级2',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业3',
        className: '班级3',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业4',
        className: '班级4',
        alarmCount: '15',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业4',
        className: '班级4',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '10',
    },
]);
const getAlarmZy = () => {
  alarmZy(searchParams.value)
    .then(result => {
      console.log(result,'result各专业预警情况');
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data各专业预警情况');
        instituteAlarmSituationData.value = data;
      }
    })
}
</script>

<style lang="scss" scoped>
@use '@/styles/responsive.scss' as *;
.alarm-visualization {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  background:#00062E;
  color: white;
}

.header-main-bg {
  width: 100%;
  height:55vh;
  background-image: url('/img/visualization/headBg.png');
  background-size: 100% 100%;
  box-sizing: border-box;
   .head-title{
    font-family: YouSheBiaoTiHei;
    font-size: vw(34px);
    color: #FFFFFF;
    line-height: vh(58px);
    letter-spacing: 3px;
    text-shadow: 0px 3px 4px #00182E;
    text-align: center;
    font-style: normal;
  }
  .title-selectors{
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .left-title{
      display: flex;
      align-items: center;
      width:vw(330px) ;
      height:vh(52px) ;
      font-family: YouSheBiaoTiHei;
      font-size: vw(26px);
      color: #FFFFFF;
      line-height: vh(52px);
      text-shadow: 0px 3px 4px #00182E;
      text-align: center;
      font-style: normal;
      background-image: url('/img/visualization/titleBg.png');
      background-size: 95% 110%;
      background-repeat: no-repeat;
      background-position: center;
      img{
        width:vw(18px) ;
        height:vh(28px) ;
        margin-left: vw(30px);
        margin-right: vw(10px);
      }
      .text1{
        // height:vh(52px);
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: vh(18px);
        color: #FFFFFF;
        line-height:vh(39px);
        text-shadow: 0px 0px 6px rgba(52,255,239,0.6), 0px 1px 1px rgba(0,30,73,0.5);
        font-style: normal;
        margin-left: vw(8px);
        margin-right: vw(16px);
        align-self: center;
      }
      .text2{
        display: inline-block;
        width: 1px;
        height: vh(25px);
        background: #FFFFFF;
        opacity: 0.57;
      }
      .text3{
        font-family: YouSheBiaoTiHei;
        font-size: vh(30px);
        color: #FFFFFF;
        line-height: vh(39px);
        text-shadow: 0px 3px 4px #00182E;
        text-align: center;
        font-style: normal;
        margin-left: vw(16px);
      }
  }
 
  .selectors {
  display: flex;
  gap: 15px;
  margin-right: vw(30px);
}


.custom-select:deep(.el-select__wrapper) {
  width: vw(110px);
  height: vh(30px) !important;
  min-height:30px;
  background: #112F48;
  border-radius: 1px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(128, 192, 253, 0.54), rgba(145, 199, 255, 0.83)) 1 1;
   box-shadow: none;
}

    .custom-select:deep(.el-select__placeholder) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: vw(13px);
      color: #F5F8FF;
      line-height: 17px;
      text-align: left;
      font-style: normal;
    }

.custom-select:deep(.is-transparent) {
  font-family: PingFangSC, PingFang SC;
font-weight: 500;
font-size: vw(13px);
color: #ABD7FF;
line-height: 20px;
text-align: left;
font-style: normal;
}

.custom-select:deep(.el-select__caret) {
    display: none; // 隐藏默认的下拉箭头
  }
  .custom-select:deep(.el-select__suffix) {
    background-image: url('/img/visualization/downImg.png'); // 使用自定义图标
    background-repeat: no-repeat;
    background-position: center;
    background-size: 300% 500%;
    width: vw(10px);
    height: vh(4px);
    box-shadow: 0px 0px 8px 2px #2EB0FF;
  }
 
  }
  
}
.main-content{
  display: flex;
  justify-content: space-between;
 margin-right: vh(30px);
    margin-left: vh(30px);
.card-content{
  width: 100%;
 height: calc(55vh - vh(168px));
background: rgba(9,27,58,0.48);

}
}
  .schoolLevel-card{
    width:32%;
  }
  .schoolLevel-card-big{
    width: 36%;
    margin-right: vh(30px);
    margin-left: vh(30px);

  }
  .schoolLevel-card-title{
   height: vh(48px);
     font-family: 'PangMenZhengDao';
  background-image: url('/img/visualization/subheadingBg.png');
  background-size: 100% 100%;
  font-size: vw(22px);
  color: #FFFFFF;
  line-height: vh(50px);
  text-align: left;
  font-style: normal;
  padding-left: vw(55px);
  margin-bottom: 10px;
}
.title-top{
  font-family: 'YouSheBiaoTiHei';
  .title-top-yellow{
    color: #FFB800;
  }
}
.schoolLevel-card-title-big{
   height: vh(48px);
     font-family: 'PangMenZhengDao';
   background-image: url('/img/visualization/subheadingBg2.png');
  background-size: 100% 100%;
  font-size: vw(22px);
  color: #FFFFFF;
  line-height: vh(48px);
  text-align: left;
  font-style: normal;
  padding-left: vw(55px);
  margin-bottom: 10px;
}
.main-content-1{
  display: flex;
  justify-content: space-between;
  height: calc(45vh - vh(60px)) !important;
  margin: vh(30px) ;
  box-sizing: border-box;
  .card-content1{
      width: 100%;
 height: calc(45vh - vh(118px));
background: rgba(9,27,58,0.48);
  }
}


</style>