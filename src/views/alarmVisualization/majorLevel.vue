<template>
  <div class="alarm-visualization">
    <!-- 顶部标题和选择器 -->
    <div class="header-main-bg">
      <div class="head-title">学情预警可视化</div>
      <div class="title-selectors">
         <div class="left-title">
        <div>四川邮电职业技术学院</div>
      </div>
      <div class="selectors">
        <div class="selector-item">
          <el-select v-model="year" placeholder="请选择年份" class="custom-select">
            <el-option label="2025年" value="2025"></el-option>
            <el-option label="2024年" value="2024"></el-option>
            <el-option label="2023年" value="2023"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="semester" placeholder="请选择学期" class="custom-select">
            <el-option label="第一学期" value="1"></el-option>
            <el-option label="第二学期" value="2"></el-option>
          </el-select>
        </div>
        <div class="selector-item">
          <el-select v-model="grade" placeholder="请选择年级" class="custom-select">
            <el-option label="全部年级" value="all"></el-option>
            <el-option label="一年级" value="1"></el-option>
            <el-option label="二年级" value="2"></el-option>
            <el-option label="三年级" value="3"></el-option>
          </el-select>
        </div>
      </div>
      </div>
    <!-- 主体内容 -->
     <div class="main-content">
       <!-- 各班级预警学生分布 -->
        <div class="schoolLevel-card">
          <div class="schoolLevel-card-title">各班级预警学生分布</div>
          <div class="card-content">
            <classAlarm :data="classAlarmData" ref="classAlarmRef" style="width: 100%; height: 100%;"/>
          </div>
        </div>
          
          
       
        <!-- 全校预警概况 -->
        <div class="schoolLevel-card-big ">
          <div class="schoolLevel-card-title-big">全校预警概况</div>
          <div class="card-content">
             <div class="overview">
              <div class="overview-item1">
                <div class="overview-item1-title">总预警人数</div>
                <div class="overview-item1-content"><span>{{totalWarningStudents}}</span> <span>人</span></div>
              </div>
              <div class="overview-item2">
                <div class="overview-item2-content"><span>{{warningRate}}</span> <span>%</span></div>
                <div class="overview-item2-title">首修预警率</div>
              </div>
              <div class="overview-item3">
                <div class="overview-item1-title">总预警次数</div>
                <div class="overview-item1-content"><span>{{totalWarningTimes}}</span> <span>人</span></div>
              </div>
             </div>
             <div class="overview1">
              <div class="overview1-item">
                <img src="/img/visualization/icon1.png" alt="">
              </div>
              <div class="overview1-item">
                <img src="/img/visualization/icon2.png" alt="">
              </div>
              <div class="overview1-item">
                <img src="/img/visualization/icon3.png" alt="">
              </div>
             </div>
             <div class="overview2">
                <div>课程成绩预警数</div>
                <div>毕业设计预警数</div>
                <div>顶岗实习预警数</div>
             </div>
          </div>
        </div>

        <!-- 学院近5年预警趋势 -->
        <div class="schoolLevel-card ">
          <div class="schoolLevel-card-title">各班级预警处理情况</div>
          <div class="card-content">
            <alarmHandle :data="alarmHandleData" ref="alarmHandleRef" style="width: 100%; height: 100%;" />
          </div>
         
        </div>
     </div>
       
     </div>
      <!-- 主体内容 -->
       <div class=" main-content-1">
        <!-- 各学院预警处理情况 -->
        <div class="schoolLevel-card handling-situation">
          <div class="schoolLevel-card-title">重点预警班级追踪 <span class="title-top">TOP<span class="title-top-yellow">5</span></span> </div>
          <div class="card-content1">
            <classTracking :data="classTrackingData" ref="classTrackingRef" style="width: 100%; height: 100%;" />
          </div>
         
        </div>
        <!-- 各学院预警率排序 -->
        <div class="schoolLevel-card-big ranking">
          <div class="schoolLevel-card-title">重点预警课程追踪 <span class="title-top">TOP<span class="title-top-yellow">5</span></span> </div>
          <div class="card-content1">
            <classTracking :data="classTrackingData" ref="classTrackingRef" style="width: 100%; height: 100%;" />
            
          </div>
        </div>
        <!-- 学院预警专业 TOP5 --> 
        <div class="schoolLevel-card top5-profession">
          <div class="schoolLevel-card-title">重点预警学生追踪 <span class="title-top">TOP<span class="title-top-yellow">5</span></span> </div>
          <div class="card-content1">
            <classTracking :data="classTrackingData" ref="classTrackingRef" style="width: 100%; height: 100%;" />
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import alarmHandle from './components/alarmHandle.vue'
import classAlarm from './components/classAlarm.vue'
import classTracking from './components/classTracking.vue';

const year = ref('');
const semester = ref('');
const grade = ref('all');
const totalWarningStudents = ref(1000);
const warningRate = ref(28);
const totalWarningTimes = ref(500);

// 各学院预警率排序数据
const rankingData = ref([
  { name: '通信工程学院', rate: 30 },
  { name: '经济管理学院', rate: 29 },
  { name: '信息工程学院', rate: 25 },
  { name: '军士教育学院', rate: 25 }
]);

// 专业TOP5数据
const top5Data = ref([
  { name: '人工智能', count: 90 },
  { name: '现代物业管理', count: 80 },
  { name: '软件技术', count: 70 },
  { name: '计算机网络', count: 60 },
  { name: '物联网工程', count: 50 }
]);

// 初始化图表
onMounted(() => {

});
onUnmounted(() => {

});


//各班级预警处理情况
const alarmHandleData = ref({
  seriesData1: [10, 20, 30, 40, 50, 70],
  seriesData2: [20, 30, 30, 10, 50, 60],
  xAxisData: ['2024', '2025', '2026', '2027', '2028', '2029'],
});
//各班级预警学生分布
const classAlarmData = ref({
  seriesData1: [10, 20, 30, 40, 50, 70],
  xAxisData: ['2024', '2025', '2026', '2027', '2028', '2029'],
});

//各班级预警处理情况
const classTrackingData = ref([
    {
        name: '专业1',
        className: '班级1',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业2',
        className: '班级2',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业3',
        className: '班级3',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业4',
        className: '班级4',
        alarmCount: '15',
        handleCount: '20',
        alarmHandleRate: '30',
    },
    {
        name: '专业4',
        className: '班级4',
        alarmCount: '10',
        handleCount: '20',
        alarmHandleRate: '10',
    },
]);

// 进度条颜色
function progressColor(value) {
  if (value >= 90) return '#FFA500';
  else if (value >= 70) return '#409EFF';
  else if (value >= 50) return '#67C23A';
  else return '#F56C6C';
}
</script>

<style lang="scss" scoped>
@use '@/styles/responsive.scss' as *;
@use '@/styles/utils.scss' as utils;
.alarm-visualization {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  background:#00062E;
  color: white;
  min-width: 1200px;
}

.header-main-bg {
  width: 100%;
  height:55vh;
  background-image: url('/img/visualization/headBg.png');
  background-size: 100% 100%;
  box-sizing: border-box;
   .head-title{
    font-family: YouSheBiaoTiHei;
    font-size: utils.vw(34px);
    color: #FFFFFF;
    line-height: utils.vh(58px);
    letter-spacing: 3px;
    text-shadow: 0px 3px 4px #00182E;
    text-align: center;
    font-style: normal;
  }
  .title-selectors{
    display: flex;
    justify-content: space-between;
    .left-title{
      width:utils.vw(330px) ;
      height:utils.vh(52px) ;
      font-family: YouSheBiaoTiHei;
      font-size: utils.vw(26px);
      color: #FFFFFF;
      line-height: utils.vh(38px);
      text-shadow: 0px 3px 4px #00182E;
      text-align: center;
      font-style: normal;
      background-image: url('/img/visualization/titleBg.png');
      background-size: 100% 100%;
  }
 
  .selectors {
  display: flex;
  gap: utils.vw(15px);
  margin-right: utils.vw(30px);
}


.custom-select:deep(.el-select__wrapper) {
  width: utils.vw(110px);
  height: utils.vh(30px) !important;
  min-height:30px;
  background: #112F48;
  border-radius: 1px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(128, 192, 253, 0.54), rgba(145, 199, 255, 0.83)) 1 1;
   box-shadow: none;
}

    .custom-select:deep(.el-select__placeholder) {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: utils.vw(13px);
      color: #F5F8FF;
      line-height: 17px;
      text-align: left;
      font-style: normal;
    }

.custom-select:deep(.is-transparent) {
  font-family: PingFangSC, PingFang SC;
font-weight: 500;
font-size: utils.vw(13px);
color: #ABD7FF;
line-height: utils.vh(20px);
text-align: left;
font-style: normal;
}

.custom-select:deep(.el-select__caret) {
    display: none; // 隐藏默认的下拉箭头
  }
  .custom-select:deep(.el-select__suffix) {
    background-image: url('/img/visualization/downImg.png'); // 使用自定义图标
    background-repeat: no-repeat;
    background-position: center;
    background-size: 300% 500%;
    width: utils.vw(10px);
    height: utils.vh(4px);
    box-shadow: 0px 0px 8px 2px #2EB0FF;
  }
 
  }
  
}
.main-content{
  display: flex;
  justify-content: space-between;
 margin-right: vh(30px);
    margin-left: vh(30px);
.card-content{
  width: 100%;
 height: calc(55vh - vh(168px));
background: rgba(9,27,58,0.48);
 .overview{
  height: 52%;
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  padding-top: 12px;
  .overview-item1{
    width: 30%;
    height: vh(80px);
   background-image: url('/img/visualization/leftImg.png');
   background-size: 100% 100%;
  }
  .overview-item2{
    width: vw(200px);
    height:vh(200px);
   background-image: url('/img/visualization/roundBg.png');
   background-size: 100% 100%;
   text-align: center;
  }
  .overview-item3{
    width: 30%;
    height: vh(80px);
    background-image: url('/img/visualization/rightImg.png');
    background-size: 100% 100%;
  }
  
 }
 .overview1{
  height: 36%;
  display: flex;
  justify-content: space-between;
   padding: 0px 35px 0px 35px;
  .overview1-item{
    width: 28%;
    height: 100%;
   background-image: url('/img/visualization/iconBg.png');
   background-size: 100% 100%;
   text-align: center;

   img{
    width: vw(48px);
    height: vh(43px);
    margin-top: 10px;
   }
  }

 }
 .overview2{
    display: flex;
  justify-content: space-around;
  align-items: center;
  align-content: center;
    padding:0px 10px 30px 15px;
    // div{
    //   text-align: center;
    // }
 }
}
}
  .schoolLevel-card{
    width:32%;
  }
  .schoolLevel-card-big{
    width: 36%;
    margin-right: vh(30px);
    margin-left: vh(30px);

  }
  .schoolLevel-card-title{
  font-family: 'PangMenZhengDao';
  height: vh(48px);
  background-image: url('/img/visualization/subheadingBg.png');
  background-size: 100% 100%;
  font-size: vw(24px);
  color: #FFFFFF;
  line-height: vh(50px);
  text-align: left;
  font-style: normal;
  padding-left: vw(50px);
  margin-bottom: 10px;
}
.schoolLevel-card-title-big{
   height: vh(48px);
   font-family: 'PangMenZhengDao';
   background-image: url('/img/visualization/subheadingBg2.png');
  background-size: 100% 100%;
  font-size: vw(24px);
  color: #FFFFFF;
  line-height: vh(48px);
  text-align: left;
  font-style: normal;
  padding-left: vw(50px);
  margin-bottom: 10px;
}
.main-content-1{
  // width: 100%;
  display: flex;
  justify-content: space-between;
  height: calc(45vh - vh(60px)) !important;
  margin: vh(30px) ;
  box-sizing: border-box;
  .card-content1{
      width: 100%;
 height: calc(45vh - vh(118px));
background: rgba(9,27,58,0.48);
  }
}
.title-top{
  font-family: 'YouSheBiaoTiHei';
  .title-top-yellow{
    color: #FFB800;
  }
}

</style>