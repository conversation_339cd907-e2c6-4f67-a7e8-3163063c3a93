<!-- 辅导员-班级 / 校领导-院系专业 / 校领导-班级  列表-->
<template>
  <cm-container>
    <div class="warning-info">
         <!-- 温馨提示 -->
      <div class="warning-tip">
        【温馨提示】以下预警信息基于教务系统产生，最终解释权归教务处所有。
      </div>
      <!-- 搜索框部分 -->
      <div class="search-box">
        <el-select v-model="searchParams.grade" v-if="role=='1'"  placeholder="请选择年级">
               <el-option
            v-for="item in njList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
            </el-select>
             <el-select v-model="searchParams.college" v-if="role=='1'" placeholder="请选择院系">
              <el-option
            v-for="item in yxList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
            </el-select>
              <el-select v-model="searchParams.major" filterable placeholder="请选择专业">
             <el-option
            v-for="item in zyList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
            </el-select>
              <el-select v-model="searchParams.class" v-if="classDetails==true||role=='2'" filterable placeholder="请选择班级">
              <el-option
            v-for="item in bjList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
            </el-select>
               <el-select v-model="searchParams.processStatus" placeholder="请选择处理状态">
              <el-option label="未处理" value="0"></el-option>
              <el-option label="已处理" value="1"></el-option>
            </el-select>
         <div class="search-box-btn">
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <!-- 表格部分 -->
       <div class="warning-info-table" ref="tableRef">
        <el-table :data="tableData"  style="width: 100%" :height="tableHeight">
          <el-table-column
          label="序号"
      type="index"
      width="60">
    </el-table-column>
        <el-table-column v-for="column in columns" :key="column.prop" :prop="column.prop" :label="column.label" :width="column.width">
          <template #default="scope">
           
              <!-- 操作列的具体内容 -->
            <span v-if="column.prop === 'operation'">
              <el-button size="mini" type="text" @click="handle(scope.$index, scope.row)">批量处理</el-button>
              <el-button size="mini" type="text" @click="detailsList(scope.$index, scope.row)">查看明细</el-button>
              <!-- <el-button size="mini" type="text" @click="details(scope.$index, scope.row)">查看处理</el-button> -->
            </span>
             <!-- 自定义列内容 -->
             <span v-else-if="column.prop === 'processStatus' || column.prop === 'latestStatus'">
                <span :style="{ color: getStatusColor(scope.row[column.prop]) }">
                  {{ scope.row[column.prop] }}
                </span>
              </span>
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
       <div class="cm-pagination-right">
          <el-pagination v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size" background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total" @change="onLoad" />
        </div>
       </div>
      
    </div>
  </cm-container>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router'
import CmContainer from '@/components/cm-container/main.vue';
import { useResizeObserver } from '@/hooks/useResizeObserver'
import useBusinessData from '@/hooks/useBusinessData';
import {bjsjListApi} from '@/api/warningInfo';
const { njList, yxList, zyList,bjList } = useBusinessData('nj', 'yx', 'zy','bj');
//角色是校领导-显示院系专业列表-点击查看明细-显示班级列表  角色是辅导员-显示班级列表
const role =ref('1')  //1代表校领导 2代表辅导员
const classDetails =ref(false) 
// 搜索参数 校领导-院系专业搜索项(院系/专业/年级/处理状态)
//               -班级搜索项(院系/专业/年级/班级/处理状态)
// 辅导员-班级搜索项(年级/班级/处理状态)
const searchParams = ref({
  dwh: '', //院系号
  zyh: '',  //专业号
  bjmc: '',  //班级名称
});

//分页参数
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
})
const tableData = ref([]);
const columns = ref([]);
// 表格列定义 -  院系专业
const columns1 = [
   { prop: 'yjbh', label: '预警批次', width: '100' },
  { prop: 'xymc', label: '预警院系',},
  { prop: 'zymc', label: '预警专业',  },
  { prop: 'nj', label: '预警年级', width: '120' },
  { prop: 'yjlx', label: '产生预警类型', },
  { prop: 'warningCount', label: '预警人数', width: '80' },
  { prop: 'courseCount', label: '预警课程数', width: '100' },
  { prop: 'clzt', label: '处理状态', width: '100' },
  { prop: 'clsj', label: '处理时间', width: '150' },
  { prop: 'operation', label: '操作', width: '200' }
];
//表格列定义 - 班级
const columns2 = [
    { prop: 'yjbh', label: '预警批次', width: '100' },
  { prop: 'xymc', label: '预警院系',},
  { prop: 'zymc', label: '预警专业',  },
  { prop: 'nj', label: '预警年级', width: '120' },
  { prop: 'bjmc', label: '预警班级',},
  { prop: 'yjlx', label: '产生预警类型', },
  { prop: 'warningCount', label: '预警人数', width: '80' },
  { prop: 'courseCount', label: '预警课程数', width: '100' },
  { prop: 'clzt', label: '处理状态', width: '100' },
  { prop: 'clsj', label: '处理时间', width: '150' },
  { prop: 'operation', label: '操作', width: '200' }
];


// 表格数据 - 班级
const tableData1 = ref([
  {
   seq: '1',
    alertTime: '2022-12-12',
    college: '通信工程学院',
    major: '通信工程（本科）',
    grade: '2022级',
    class: '2022通信工程（本科）',
    warningType: '必修课程预警、选修课程预警、毕业设计预警',
    warningCount: '5人',
    courseCount: '5门',
    processStatus: '未处理',
    processTime: '-',
    operation: '批量处理 查看明细'
  },
]);

// 当前选中的标签
const radio = ref('1');
 // 表格引用
const tableRef = useTemplateRef('tableRef')
 // 表格高度
const tableHeight = ref(0)
//初始化
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0]
    const { height: _h } = entry.contentRect
    tableHeight.value = _h - 54
  })
  changelist()
})

//初始数据
const onLoad = () => {
  bjsjListApi({current:pageParams.value.current, size:pageParams.value.size, ...searchParams.value})
    .then(result => {
      const { data, code } = result.data || {};
      if (code === 200) {
        console.log(data,'data');
        
        // tableData.value = data;
        pageParams.value.total = data.total;
      }
    })
};
// 切换标签时的处理函数
const changelist = () => {
  console.log(1);
  
  if (role.value == '1') { 
    console.log(2);
      columns.value = columns1
      tableData.value = tableData1.value
  }else if(role.value== '2'){
    console.log(3);
    onLoad()
       columns.value = columns2 
  }
};
// 搜索函数
const onSearch = () => {
  console.log(searchParams.value);
};

// 清空函数
const onClear = () => {
  searchParams.value = {};
};
const router = useRouter();
// 批量处理
const handle = (index, row) => {
  console.log(index, row);
   router.push({
    path: '/warningInfo/batchProcessing',
    query: {
      id: row.id,
    },
  })
};
//查看明细
const detailsList = (index, row) => {
  if(role.value=='1'&&!classDetails.value){
    classDetails.value=true
    columns.value = columns2
     onLoad()
  }else if(role.value=='1'&&classDetails.value){
      router.push({
    path: '/warningInfo/detailedList',
    query: {
      id: row.id,
    },
    })
  }else{
     router.push({
    path: '/warningInfo/detailedList',
    query: {
      id: row.id,
    },
    })
  }
  console.log(index, row);
   
};

// 查看
const details = (index, row) => {
  console.log(index, row);
};
// 状态颜色映射函数
const getStatusColor = (status) => {
  if (status === '不合格' || status === '未处理') {
    return 'red';
  } else if (status === '已处理' || status === '合格') {
    return 'green';
  }else{
    
  }
  return ''; // 默认颜色
};

</script>

<style lang="scss" scoped>
.warning-info {
  width: 100%;
  height: 100%;
  background-color: #fff;
  
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;
    .warning-tip {
    background-color: #ffecec;
    color: #f56c6c;
    padding: 8px;
  }
  .search-box{
    padding: 0 10px;
    margin-top: 0px;
   
  }
  .warning-info-tabs{
       padding: 0 10px;
  }
   .warning-info-table {
    padding: 0 10px;
    flex: 1;
  }
}
</style>