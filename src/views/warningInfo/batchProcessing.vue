<!-- 批量处理 / 详情-->
<template>
  <cm-container>
    <div class="warning-info-details">
      <!-- 预警信息 -->
      <div class="details-card">
        <div class="details-card-content">
          <div class="details-card-content-header">
            <div
              class="details-card-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span
                class="details-card-content-header-title">
                预警信息</span>
            </div>
          </div>
          <div class="details-card-content-table">
             <el-table :data="personInfo" stripe style="width: 100%">
          <el-table-column prop="yjsj" label="预警时间" ></el-table-column>
          <el-table-column prop="xymc" label="预警院系" ></el-table-column>
          <el-table-column prop="zymc" label="预警专业" ></el-table-column>
          <el-table-column prop="nj" label="预警年级" ></el-table-column>
          <el-table-column prop="class" label="预警人数" ></el-table-column>
          <el-table-column prop="studentId" label="预警课程数" ></el-table-column>
        </el-table>
          </div>
        </div>
      </div>
       <!-- 人员课程信息 -->
       <div class="details-card">
          <div class="details-card-header">
            <img class="details-card-header-icon"
            src="/img/alarmAnalyse/card-icon.png" alt="">
             <span
            class="details-card-header-title">人员课程信息</span>
          </div>
          <div class="details-card-content">
          <div class="details-card-content-header">
            <div
              class="details-card-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span
                class="details-card-content-header-title">
                必修+实习+毕设课程信息</span>
            </div>
          </div>
          <div class="details-card-content-table">
             <el-table :data="personInfo" stripe style="width: 100%">
              <el-table-column  label="序号" type="index" width="60"></el-table-column>
          <el-table-column prop="bjmc" label="预警班级" ></el-table-column>
          <el-table-column prop="xsxh" label="学号" ></el-table-column>
          <el-table-column prop="xsxm" label="姓名" ></el-table-column>
          <el-table-column prop="kcxzmc" label="课程性质" ></el-table-column>
          <el-table-column prop="kcsxmc" label="课程属性" ></el-table-column>
          <el-table-column prop="kcbh" label="课程号" ></el-table-column>
          <el-table-column prop="kcmc" label="课程" ></el-table-column>
          <el-table-column prop="sdxf" label="实得学分" ></el-table-column>
          <el-table-column  label="实得成绩" >
             <template #default="scope">
                  <span :style="{ color: scope.row.sdcj === '不合格' ? 'red' : '' }">
                    {{ scope.row.sdcj }}
                  </span>
                </template>
          </el-table-column>
          <el-table-column prop="cjlxmc" label="成绩类型" ></el-table-column>
          <el-table-column prop="sfbg" label="是否必过" ></el-table-column>
        </el-table>
          </div>
        </div>
      </div>
      <!-- 选修课程信息 -->
        <div class="details-card">
        <div class="details-card-content">
          <div class="details-card-content-header">
            <div
              class="details-card-content-header-title-box">
              <el-icon color="#2d55eb" size="18">
                <Document />
              </el-icon>
              <span
                class="details-card-content-header-title">
                选修课程信息</span>
            </div>
          </div>
          <div class="details-card-content-table">
           <el-table :data="courseInfo" stripe style="width: 100%">
             <el-table-column  label="序号" type="index" width="60"></el-table-column>
            <el-table-column prop="bjmc" label="预警班级"></el-table-column>
            <el-table-column prop="xsxh" label="学号"></el-table-column>
            <el-table-column prop="xsxm" label="姓名"></el-table-column>
            <el-table-column prop="kcsxmc" label="课程属性"></el-table-column>
            <el-table-column prop="yxkc" label="已修课程"></el-table-column>
            <el-table-column label="待修课程">
                <template #default="scope">
                  <span :style="{ color: scope.row.xxkc === '不合格' ? 'red' : '' }">
                    {{ scope.row.xxkc }}
                  </span>
                </template>
              </el-table-column>
  </el-table>
          </div>
        </div>
      </div>
      <!-- 预警处理 -->
       <div class="details-card">
          <div class="details-card-header">
            <img class="details-card-header-icon"
            src="/img/alarmAnalyse/card-icon.png" alt="">
             <span
            class="details-card-header-title">预警处理</span>
          </div>
          <div class="details-card-content-table">
             <el-form :model="handleForm" label-width="120px" :rules="rules" ref="handleFormRef">
          <el-form-item label="干预措施"  prop="clyj">
            <el-input type="textarea" rows="4"   v-model="handleForm.clyj"></el-input>
          </el-form-item>
          <el-row>
                <el-col :span="8">
                  <el-form-item label="再次提醒日期">
                    <el-date-picker v-model="handleForm.zctxsj" style="width: 100%;"  type="date" placeholder="请选择日期"></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                   <el-form-item label="处理状态">
                     <el-select v-model="handleForm.clzt"disabled placeholder=" ">
              <el-option label="未处理" value="0"></el-option>
              <el-option label="已处理" value="1"></el-option>
            </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="处理时间">
                    <el-input v-model="handleForm.clsj" disabled></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
        </el-form>
          </div>
      </div>
    </div>
     <!-- 按钮 -->
    <div class="details-footer">
      <el-button @click="onCancel">取 消</el-button>
      <el-button @click="onSubmit" type="primary">保 存</el-button>
    </div>
  </cm-container>
</template>

<script setup>
import { ref } from 'vue';
import CmContainer from '@/components/cm-container/main.vue';
import { useRouter } from 'vue-router'
const route = useRoute()
const rowData = route.query.rowData
const type = route.query.type
// 预警时间人员信息数据
const personInfo = ref([
  {
    "alertTime": "2022-12-12",
    "grade": "2022",
    "college": "通信工程学院",
    "major": "通信工程（本科）",
    "class": "2022通信工程（本科）",
    "studentId": "123123",
    "name": "张三"
  }
]);

// 预警课程信息数据
const courseInfo = ref([
   {
    "academicYearSemester": "2021-2022第一学期",
    "courseNature": "实践技能模块",
    "courseAttribute": "必修",
    "courseCode": "Z00000960",
    "courseName": "宽带接入工程实训",
    "creditsEarned": "0（总4）",
    "actualScore": "50（总100）",
    "scoreType": "首修",
    "isRequiredPass": "是",
    "latestCourseStatus": "不合格"
  }
]);
//预警信息
const warningInfo = ref([
  {
    "alertTime": "2022-12-12",
    "grade": "2022",
    "college": "通信工程学院",
    "major": "通信工程（本科）",
    "class": "2022通信工程（本科）",
    "studentId": "123123",
    "name": "张三",
    "designScore":"50",
    "scoreType":"首修",
    "latestStatus":"不合格"
  }
]);

// 预警处理表单数据
const handleForm = ref({
  clyj: "",
  zctxsj: "",
});
const router = useRouter()
// 取消按钮点击事件
const onCancel = () => {
  // 取消逻辑
  router.back()
};
const rules = ref({
  clyj: [{ required: true, message: '请输入干预措施', trigger: 'blur' }],
})
const handleFormRef = useTemplateRef('handleFormRef')
// 提交按钮点击事件
const onSubmit = () => {
  // 提交逻辑
   handleFormRef.value.validate((valid) => {
    if (valid) {
        console.log(handleForm.value);
        
    }else{
      console.log('error submit!!');
      return false;
    }
  })
};
</script>

<style lang="scss" scoped>
.warning-info-details {
  height: calc(100% - 70px);
  overflow-y: auto;

  .details-card {
    width: 100%;
    background: #fff;
    border-radius: 4px;
    border: 2px solid #FFFFFF;
    backdrop-filter: blur(10px);
    box-sizing: border-box;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    .details-card-header {
      display: flex;
      align-items: center;
      gap: 14px;
      margin-bottom: 20px;

      .details-card-header-icon {
        width: 24px;
        height: 24px;
      }

      .details-card-header-title {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }
    }

    .details-card-content {
      background: #fcfcfc;
      border-radius: 4px;
      padding: 20px;
      box-sizing: border-box;
      height: 100%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .details-card-content-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .details-card-content-header-title {
          font-family: AppleSystemUIFont;
          font-size: 13px;
          color: #333333;
        }

        .details-card-content-header-title-box {
          display: flex;
          align-items: center;
          gap: 6px;
        }
      }

      .details-card-content-table {
        margin-top: 20px;
      }
    }


  }

}
.details-footer {
  margin-top: 10px;
  height: 60px;
  background: #fff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-sizing: border-box;
}

</style>