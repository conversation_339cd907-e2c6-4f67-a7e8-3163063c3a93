import { getStore, setStore } from '@/utils/store';
import { getDictionary } from '@/api/system/dict';

const dict = {
  state: {
    flowRoutes: getStore({ name: 'flowRoutes' }) || {},
    dict: [],
  },
  getters: {
    getDict: state => _key => {
      if (_key == null || _key === '') {
        return null;
      }
      try {
        for (let i = 0; i < state.dict.length; i++) {
          let item = state.dict[i];
          if (item.key === _key) {
            return item.value;
          }
        }
      } catch (e) {
        void e;
        return null;
      }
      return null; // 如果未找到对应的字典项，返回null
    },
  },
  actions: {
    FlowRoutes({ commit }) {
      return new Promise((resolve, reject) => {
        getDictionary({ code: 'flow' })
          .then(res => {
            commit('SET_FLOW_ROUTES', res.data.data);
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    getDict({ state }, _key) {
      if (_key == null || _key === '') {
        return null;
      }
      try {
        for (let i = 0; i < state.dict.length; i++) {
          let item = state.dict[i];
          if (item.key === _key) {
            return item.value;
          }
        }
      } catch (e) {
        return null;
      }
      return null; // 如果未找到对应的字典项，返回null
    },

    // 设置字典
    setDict({ state }, { key, value }) {
      if (key !== null && key !== '') {
        state.dict.push({ key, value });
      }
    },

    // 删除字典
    removeDict({ state }, _key) {
      let removed = false;
      try {
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key === _key) {
            state.dict.splice(i, 1);
            removed = true;
            break; // 找到并移除后直接退出循环
          }
        }
      } catch (e) {
        removed = false;
      }
      return removed;
    },

    // 清空字典
    cleanDict({ state }) {
      state.dict = []; // 直接赋空数组即可清空字典
    },
  },
  mutations: {
    SET_FLOW_ROUTES: (state, data) => {
      state.flowRoutes = data.map(item => {
        return {
          routeKey: `${item.code}_${item.dictKey}`,
          routeValue: item.remark,
        };
      });
      setStore({ name: 'flowRoutes', content: state.flowRoutes });
    },
  },
};

export default dict;
