import { ref, toRefs } from "vue";
import store from "@/store";
import { getDictionary } from "@/api/system/dictbiz";
import { toCamelCase } from "@/utils/util";
/**
 * 获取字典数据
 */
const useDict = (...args) => {
  const res = ref({});
  return (() => {
    args.forEach(async (dictType) => {
      const dictName = toCamelCase(dictType) + "DictData";
      res.value[dictName] = [];
      const dicts = store.getters.getDict(dictName);
      if (dicts) {
        res.value[dictName] = dicts;
      } else {
        getDictionary({ code: dictType }).then((resp) => {
          const { code, data } = resp.data;
          if (code === 200) {
            res.value[dictName] = data.map((d) => ({
              label: d.dictValue,
              value: d.dictKey,
            }));
            store.dispatch("setDict", { key: dictName, value: res.value[dictName] });
          }
        });
      }
    });
    return toRefs(res.value);
  })();
};
export default useDict;
