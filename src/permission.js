import router from "./router/";
import store from "./store";
import { getToken } from "@/utils/auth";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { ElLoading } from "element-plus";
NProgress.configure({ showSpinner: false });

const casLoginFn = (next) => {
  if (!to.query.cas && globalThis.__CAS_LOGIN) {
    const loading = ElLoading.service({
      lock: true,
      text: "登录中,请稍后",
      background: "rgba(0, 0, 0, 0.7)",
    });
    store
      .dispatch("LoginByCas")
      .then(() => {
        store.dispatch("FlowRoutes").then(() => {});
        next(store.getters.tagWel || "/");
      })
      .finally(() => {
        loading.close();
      });
  } else {
    next("/login");
  }
};
const lockPage = "/lock"; //锁屏页
router.beforeEach((to, from, next) => {
  // 获取匹配的路由数组
  const matchedRoutes = to.matched;
  const component = matchedRoutes.length > 0 ? matchedRoutes[matchedRoutes.length - 1].components.default : null;
  if (component && typeof component == "function") {
    component().then((mod) => {
      mod.default.name = to.fullPath;
    });
  }
  const meta = to.meta || {};
  const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
  store.commit("SET_IS_MENU", isMenu === undefined);

  if (getToken()) {
    if (store.getters.isLock && to.path !== lockPage) {
      //如果系统激活锁屏，全部跳转到锁屏页
      next({ path: lockPage });
    } else if (to.meta.permission && !store.getters.permission[to.meta.permission]) {
      next({ path: "/403" });
    } else if (to.path === "/login") {
      //如果登录成功访问登录页跳转到主页
      next({ path: "/" });
    } else {
      if (store.getters.token.length === 0) {
        store.dispatch("FedLogOut").then(() => {
          casLoginFn(next);
        });
      } else {
        const meta = to.meta || {};
        const query = to.query || {};
        if (meta.target) {
          window.open(query.url.replace(/#/g, "&"));
          return;
        } else if (meta.isTab !== false && meta.isNewTab !== 1) {
          store.commit("ADD_TAG", {
            name: query.name || to.name,
            path: to.path,
            fullPath: to.path,
            params: to.params,
            query: to.query,
            meta: meta,
          });
        }
        next();
      }
    }
  } else {
    //判断是否需要认证，没有登录访问去登录页
    if (meta.isAuth === false) {
      next();
    } else {
      casLoginFn(next);
    }
  }
});

router.afterEach((to) => {
  NProgress.done();
  let title = router.$avueRouter.generateTitle(to, { label: "name" });
  router.$avueRouter.setTitle(title);
  store.commit("SET_IS_SEARCH", false);
});
