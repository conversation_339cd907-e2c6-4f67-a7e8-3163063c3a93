/**
 * v-perms 权限控制自定义指令
 * 简化版实现，仅支持基于权限码的验证
 * 使用方式：
 * v-perms="'user:add'"                    // 单个权限码
 * v-perms="['user:edit', 'user:update']"  // 权限码数组（OR逻辑）
 */

import { hasAuth } from '@/utils/auth'

/**
 * 处理元素的显示/隐藏
 * @param {HTMLElement} el - DOM元素
 * @param {boolean} hasAuth - 是否有权限
 */
function handleElementVisibility(el, hasAuth) {
  if (hasAuth) {
    // 有权限时，确保元素可见（如果之前被隐藏）
    if (el.style.display === 'none') {
      el.style.display = ''
    }
  } else {
    // 无权限时，从DOM中移除元素
    if (el.parentNode) {
      el.parentNode.removeChild(el)
    }
  }
}

/**
 * v-perms 指令定义
 */
export const perms = {
  // Vue 3 生命周期：元素挂载到DOM时
  mounted(el, binding) {
    const permission = binding.value
    const _hasAuth = hasAuth(permission)
    handleElementVisibility(el, _hasAuth)
  },

  // Vue 3 生命周期：指令绑定值更新时
  updated(el, binding) {
    const permission = binding.value
    const _hasAuth = hasAuth(permission)
    handleElementVisibility(el, _hasAuth)
  }
}
