import request from '@/axios';
//待办-班级列表
export const classTodoPageApi = (params) => {
    return request({
      url: '/blade-yjxxcj/yjxxCj/classTodoPage',
      method: 'get',
      params
    });
};
//待办-列表
export const todoPageApi = (params) => {
    return request({
      url: '/blade-yjxxcj/yjxxCj/todoPage',
      method: 'get',
      params
    });
};
  

//待办数量
export const todoPageCountApi = () => {
    return request({
      url: '/blade-yjxxcj/yjxxCj/todoPageCount',
      method: 'get',
    });
};

// 已办-列表
export const donePageApi = (params) => {
  return request({
    url: '/blade-yjxxcj/yjxxCj/donePage',
    method: 'get',
    params
  });
};

// 已办数量
export const donePageCountApi = () => {
  return request({
    url: '/blade-yjxxcj/yjxxCj/donePageCount',
    method: 'get',
  });
};
