import request from '@/axios';
//近五年课程预警趋势
export const getAlarmTrends = (params) => {
    return request({
      url: '/blade-kcAnalysis/kcAnalysis/alarmTrends',
      method: 'get',
      params
    });
};
  

//综合数据
export const getCompositeData = () => {
    return request({
      url: '/blade-bjsj/zzwd/compositeData',
      method: 'get',
    });
};
  
// 获取课程预警排名
export const alarmRank = () => {
  return request({
    url: '/blade-kcAnalysis/kcAnalysis/alarmRank',
    method: 'get',
  });
};

// 获取综合数据分析
export const comData = () => {
  return request({
    url: '/blade-kcAnalysis/kcAnalysis/comData',
    method: 'get',
  });
};