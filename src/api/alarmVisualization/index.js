import request from '@/axios';
//综合数据分析
export const comData = () => {
    return request({
      url: '/blade-kcAnalysis/kcAnalysis/comData',
      method: 'get',
    });
};
  
//校级大屏   各学院预警学生分布
export const alarmDistribution = () => {
    return request({
      url: '/blade-xxScreen/xxScreen/alarmDistribution',
      method: 'get',
    });
};
//校级大屏   各学院预警学生处理情况
export const alarmHandle = () => {
    return request({
      url: '/blade-xxScreen/xxScreen/alarmHandle',
      method: 'get',
    });
};
//校级大屏   学院预警率排行
export const yxAlarmRank = () => {
    return request({
      url: '/blade-xxScreen/xxScreen/yxAlarmRank',
      method: 'get',
    });
};
//校级大屏   各学院近五年预警趋势
export const xxAlarmTrends = () => {
    return request({
      url: '/blade-xxScreen/xxScreen/xxAlarmTrends',
      method: 'get',
    });
};
//校级大屏   预警专业排行
export const alarmZyRank = () => {
    return request({
      url: '/blade-yxScreen/yxScreen/alarmZyRank',
      method: 'get',
    });
};

//院级大屏   各专业预警学生分布
export const yxAlarmDistribution = () => {
    return request({
      url: '/blade-yxScreen/yxScreen/alarmDistribution',
      method: 'get',
    });
};
//院级大屏   各专业预警处理情况
export const alarmZyHandle = () => {
    return request({
      url: '/blade-yxScreen/yxScreen/alarmZyHandle',
      method: 'get',
    });
};
//院级大屏   预警专业排行
export const yxAlarmZyRank = () => {
    return request({
      url: '/blade-yxScreen/yxScreen/alarmZyRank',
      method: 'get',
    });
};
//院级大屏   专业预警情况
export const alarmZy = () => {
    return request({
      url: '/blade-yxScreen/yxScreen/alarmZy',
      method: 'get',
    });
};