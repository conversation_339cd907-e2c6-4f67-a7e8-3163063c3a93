import request from '@/axios';

export const getListAPI = (pageNum, pageSize, params) => {
  return request({
    url: '/blade-yj/yj/getNjXkSz',
    method: 'get',
    params: {
      ...params,
      pageNum,
      pageSize,
    },
  });
};

/**
 * 批量设置毕业设计或顶岗实习（勾选）
 * @param {*} data
 * @returns
 */
export const saveYjBatchByCheckAPI = data => {
  return request({
    url: '/blade-yj/yj/saveYjBatchByCheck',
    method: 'post',
    data,
  });
};
/**
 * 批量设置毕业设计或顶岗实习（全部）
 * @param {*} data
 * @returns
 */
export const saveYjBatchAPI = data => {
  return request({
    url: '/blade-yj/yj/saveYjBatch',
    method: 'post',
    data,
  });
};

/**
 * 批量设置预警时间（全部）
 * @param {*} data
 * @returns
 */
export const saveYjSjBatchAPI = data => {
  return request({
    url: '/blade-yjsj/yjSj/batchConfigSj',
    method: 'post',
    data,
  });
};
/**
 * 批量设置预警时间（勾选）
 * @param {*} data
 * @returns
 */
export const saveYjSjBatchByCheckAPI = data => {
  return request({
    url: '/blade-yjsj/yjSj/batchConfigSjByCheck',
    method: 'post',
    data,
  });
};

/**
 * 批量设置课程（勾选）
 * @param {*} data
 * @returns
 */
export const saveYjKcBatchAPI = data => {
  return request({
    url: '/blade-yjxx/yjXx/batchConfigYjByCheck',
    method: 'post',
    data,
  });
};

/**
 * 批量设置课程（全部）
 * @param {*} data
 * @returns
 */
export const saveYjKcBatchAllAPI = data => {
  return request({
    url: '/blade-yjxx/yjXx/batchConfigYj',
    method: 'post',
    data,
  });
};

/**
 * 保存课程设置列表（详情页面）
 * @param {*} data
 * @returns
 */
export const saveXxKcAPI = data => {
  return request({
    url: '/blade-yjxx/yjXx/configYj',
    method: 'post',
    data,
  });
};

/**
 * 获取课程设置列表（详情页面）
 * @param {*} data
 * @returns
 */
export const getXxKcAPI = params => {
  return request({
    url: '/blade-yjxx/yjXx/getYjXx',
    method: 'get',
    params,
  });
};
/**
 * 清空设置（列表）
 * @param {*} data
 * @returns
 */
export const clearSettingAPI = data => {
  return request({
    url: '/blade-yj/yj/saveYj',
    method: 'post',
    data,
  });
};

/**
 * 获取课程设置列表（详情页面）
 * @param {*} data
 * @returns
 */
export const getYjkcDetailAPI = params => {
  return request({
    url: '/blade-yjkc/yjKc/getYjkcDetail',
    method: 'get',
    params,
  });
};

/**
 * 获取专业设置列表（详情页面）
 * @param {*} data
 * @returns
 */
export const getZyDetailAPI = params => {
  return request({
    url: '/blade-zyszsj/zyszsj/getZysj',
    method: 'get',
    params,
  });
};

/**
 * 获取预警时间设置列表（详情页面）
 * @param {*} data
 * @returns
 */
export const getYjsjDetailAPI = params => {
  return request({
    url: '/blade-yjsj/yjSj/getyjSj',
    method: 'get',
    params,
  });
};

/**
 * 保存设置（详情页面）
 * @param {*} data
 * @returns
 */
export const saveKcConfigAPI = data => {
  return request({
    url: '/blade-yjkc/yjKc/saveKcConfig',
    method: 'post',
    data,
  });
};
