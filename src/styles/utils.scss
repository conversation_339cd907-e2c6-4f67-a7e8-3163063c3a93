@use 'sass:math';

// 设计稿基准宽度（根据实际设计稿修改）
$designWidth: 1920px;
$designHeight: 1080px;

// 将 px 转换为 vw（宽度适配）
@function vw($px) {
  $minWidth: 1600px; // 设置最小视口宽度为1600px
  $value: math.div($px, $designWidth) * 100vw; // 将像素值转换为设计稿宽度的百分比（设计稿宽度1920px时，1920px对应100vw）
  $minValue: math.div($px, $designWidth) * $minWidth; // 计算最小宽度下的像素值
  $rounded: math.div(math.round($value * 1000000), 1000000); // 将计算结果保留6位小数
  $roundedMin: math.div(math.round($minValue * 1000000), 1000000); // 将最小宽度计算结果保留6位小数
  @return max(#{$rounded}, #{$roundedMin}); // 返回计算值和最小宽度值中的较大者
}
@function vh($px) {
  $value: math.div($px, $designHeight) * 100vh; // 将像素值转换为设计稿高度的百分比（设计稿高度1080px时，1080px对应100vh）
  $rounded: math.div(math.round($value * 1000000), 1000000); // 将计算结果保留6位小数
  @return #{$rounded};
}
