@use './variables.scss' as variables;

.avue-top {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  height: variables.$top_height;
  box-sizing: border-box;
  white-space: nowrap;
  background-image: url('/img/top/avue-header-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  .top-bar__left {
    height: variables.$top_height;
    display: flex;
    align-items: center;
    .logo {
      width: 260px;
    }
  }
  .top-bar__right {
    display: flex;
    align-items: center;
    .top-user {
      display: flex;
      align-items: center;
      .top-bar__img {
        margin: 0 5px;
        padding: 2px;
        width: 30px;
        height: 30px;
        border-radius: 100%;
        box-sizing: border-box;
        border: 1px solid #eee;
        vertical-align: middle;
      }
      .el-dropdown-link {
        color: #fff;
      }
    }
  }
}
