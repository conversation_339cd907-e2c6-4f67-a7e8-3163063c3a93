@use 'sass:color';
@use 'sass:math';
@use 'sass:map';
$types: primary, success, warning, danger, error, info;

$colors: () !default;
$colors: map.deep-merge(
  (
    'white': #ffffff,
    'black': #000000,
    'primary': (
      'base': #2d55eb,
    ),
    'success': (
      'base': #02ad00,
    ),
    'warning': (
      'base': #ff6300,
    ),
    'danger': (
      'base': #ff0000,
    ),
    'error': (
      'base': #f56c6c,
    ),
    'info': (
      'base': #909399,
    ),
  ),
  $colors
);
$color-white: map.get($colors, 'white') !default;
$color-black: map.get($colors, 'black') !default;
@mixin set-color-mix-level($type, $number, $mode: 'light', $mix-color: $color-white) {
  $colors: map.deep-merge(
    (
      $type: (
        '#{$mode}-#{$number}': color.mix(
            $mix-color,
            map.get($colors, $type, 'base'),
            math.percentage(math.div($number, 10))
          ),
      ),
    ),
    $colors
  ) !global;
}

:root[data-theme='go'] {
  @each $type in $types {
    --el-color-#{$type}: #{map.get($colors, $type, 'base')};
    @for $i from 1 through 9 {
      @include set-color-mix-level($type, $i, 'light', $color-white);
      --el-color-#{$type}-light-#{$i}: #{map.get($colors, $type, 'light-#{$i}')};
      --el-color-#{$type}-dark-#{$i}: #{map.get($colors, $type, 'light-#{$i}')};
    }
  }
}

.theme-go {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored:
    0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;

  .cm-table {
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  }

  .inp-text-left {
    width: 100%;
    .el-input__inner {
      text-align: left;
    }
  }
  .el-table {
    --el-table-header-bg-color: #e7ecf5;
    --el-fill-color-lighter: #f7f8fa;
    box-shadow: 0 0 10px var(--el-table-header-bg-color);
  }
  .el-table th.el-table__cell {
    font-family: 'PingFang SC';
    font-weight: 500;
    font-size: 14px;
    color: #000000;
  }
  .el-table .el-table__cell {
    font-family: 'PingFang SC';
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .el-table__inner-wrapper:before {
    content: none;
  }
  .el-descriptions-item-label {
    background: #fafdff !important;
    font-family: 'PingFang SC' !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    color: rgba(0, 0, 0, 0.85) !important;
  }
  .el-descriptions__table {
    table-layout: fixed;
  }

  .cm-pagination-right {
    display: flex;
    justify-content: flex-end;
    padding: 10px 0;
  }

  .avue-sidebar {
    background: #fff !important;
    position: relative;
    .avue-sidebar-toggle {
      position: absolute;
      top: 50%;
      right: 2px;
      z-index: 1002;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 34px;
      cursor: pointer;
      background: var(--el-bg-color);
      border: 1px solid rgb(5 5 5 / 6%);
      border-radius: 4px;
      transform: translate(12px, -50%);
    }

    .el-menu-item,
    .el-sub-menu__title {
      i,
      span {
        color: #333;
      }

      &:hover {
        background: transparent;

        i,
        span {
          color: var(--el-color-primary);
        }
      }

      &.is-active {
        &:before {
          left: auto;
          right: 0;
        }

        background-color: #f0f6ff;

        i,
        span {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .avue-logo {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #fff;
    color: #333;
    box-shadow:
      var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  }

  .avue-top {
    box-shadow:
      var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
  }
  .avue-main {
    background-color: rgb(249, 250, 251);
  }
  .avue-tags {
    margin: 5px 6px 8px 6px;
    padding-bottom: 2px;

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
      width: 20px;
      line-height: 40px;
      font-size: 18px;
      text-align: center;
    }

    &__box {
      position: relative;
      box-sizing: border-box;
      padding-right: 70px;
      width: 100%;

      .el-tabs__item {
        &:first-child {
          .is-icon-close {
            display: none;
          }
        }
      }
    }

    .el-tabs__item {
      font-size: 14px !important;
      color: rgb(75, 85, 99) !important;
      font-weight: 500 !important;
      border: 1px solid rgb(205, 218, 214) !important;
      border-radius: 3px;
      height: 30px !important;
      line-height: 35px !important;
      margin: 5px 3px !important;

      &:hover {
        color: var(--el-color-primary) !important;
        border-color: var(--el-color-primary) !important;
      }
    }

    .is-active {
      color: var(--el-color-primary) !important;
      border-color: var(--el-color-primary) !important;
    }
  }
}
