export default [
  {
    path: '/alarmLayout',
    name: 'alarmSettings',
    component: () => import('@/page/index/index.vue'),
    children: [
      {
        path: '/alarmSettings/setting',
        name: '预警设置-设置必修课程',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'alarmSettings:setting',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/alarmSettings/setting.vue'),
      },
      {
        path: '/alarmSettings/settingTake',
        name: '预警设置-设置选修课程',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'alarmSettings:takeSetting',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/alarmSettings/setting-take.vue'),
      },
      {
        path: '/warningInfo/details',
        name: '预警信息-批量处理',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'warningInfo:details',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/warningInfo/details.vue'),
      },
       {
        path: '/warningInfo/batchProcessing',
        name: '预警信息-处理',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'warningInfo:batchProcessing',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/warningInfo/batchProcessing.vue'),
      },
       {
        path: '/warningInfo/detailedList',
        name: '预警信息-查看明细',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'warningInfo:detailedList',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/warningInfo/detailedList.vue'),
      },
      {
        path: '/todo/todoDetailedList',
        name: '待办-查看明细',
        meta: {
          i18n: 'dashboard',
          menu: true,
          isTab: false,
          permission: 'todo:todoDetailedList',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/todo/todoDetailedList.vue'),
      },
    ],
  },
];
