import { createRouter, createWebHistory } from 'vue-router';
import ExtRouter from './ext/';
import PageRouter from './page/';
import ViewsRouter from './views/';
import AvueRouter from './avue-router';
import i18n from '@/lang';
import Store from '@/store/';

const modulesRoutes = import.meta.glob(
  ['/src/router/views/**/*.js', '!/src/router/views/index.js'],
  { eager: true, import: 'default' },
);

// 提取并扁平化动态导入的路由
const getDynamicRoutes = modules => {
  const routes = [];
  Object.keys(modules).forEach(key => {
    const moduleRoutes = modules[key];
    if (Array.isArray(moduleRoutes)) {
      routes.push(...moduleRoutes);
    }
  });
  return routes;
};

const dynamicRoutes = getDynamicRoutes(modulesRoutes);

//创建路由
const Router = createRouter({
  base: import.meta.env.VITE_APP_BASE,
  history: createWebHistory(import.meta.env.VITE_APP_BASE),
  // 引入路由顺序ViewsRouter必须在第一个，然后添加动态路由
  routes: [...ViewsRouter, ...dynamicRoutes, ...ExtRouter, ...PageRouter],
});
AvueRouter.install({
  store: Store,
  router: Router,
  i18n: i18n,
});

Router.$avueRouter.formatRoutes(Store.getters.menuAll, true);

export function resetRouter() {
  // 重置路由 比如用于身份验证失败，需要重新登录时 先清空当前的路有权限
  const newRouter = createRouter();
  Router.matcher = newRouter.matcher; // reset router
  AvueRouter.install(Vue, {
    router: Router,
    store: Store,
    i18n: i18n,
  });
}

export default Router;
