/* 学情预警系统 - 专业加载动画 */

/* 主容器样式 */
#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
  transition:
    opacity 0.8s ease-out,
    visibility 0.8s ease-out;
}

#loader-wrapper.fade-out {
  opacity: 0;
  visibility: hidden;
}

/* 背景粒子效果 */
#loader-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.2), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 3s linear infinite;
}

/* 主要内容容器 */
.loader-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
}

/* 中心图标容器 */
.loader-icon {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: pulse 2s ease-in-out infinite;
}

/* 书本图标 */
.book-icon {
  position: relative;
  width: 50px;
  height: 40px;
}

.book-cover {
  position: absolute;
  width: 50px;
  height: 40px;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 3px;
  transform-origin: left center;
  animation: bookFlip 3s ease-in-out infinite;
}

.book-pages {
  position: absolute;
  width: 46px;
  height: 36px;
  top: 2px;
  left: 2px;
  background: white;
  border-radius: 2px;
}

.book-pages::before,
.book-pages::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #ddd;
  border-radius: 1px;
}

.book-pages::before {
  top: 8px;
}

.book-pages::after {
  top: 16px;
}

/* 数据图表元素 */
.chart-elements {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 30px;
  height: 30px;
}

.chart-bar {
  position: absolute;
  bottom: 0;
  width: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: chartGrow 2s ease-in-out infinite;
}

.chart-bar:nth-child(1) {
  left: 0;
  height: 15px;
  animation-delay: 0s;
}

.chart-bar:nth-child(2) {
  left: 6px;
  height: 20px;
  animation-delay: 0.2s;
}

.chart-bar:nth-child(3) {
  left: 12px;
  height: 12px;
  animation-delay: 0.4s;
}

.chart-bar:nth-child(4) {
  left: 18px;
  height: 18px;
  animation-delay: 0.6s;
}

/* 预警雷达扫描 */
.radar-scanner {
  position: absolute;
  top: -30px;
  left: -30px;
  width: 25px;
  height: 25px;
  border: 2px solid rgba(255, 193, 7, 0.8);
  border-radius: 50%;
  animation: radarScan 2s linear infinite;
}

.radar-scanner::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 2px;
  background: rgba(255, 193, 7, 0.9);
  transform: translate(-50%, -50%);
  transform-origin: left center;
  animation: radarSweep 2s linear infinite;
}

/* 标题样式 */
.loader-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: titleGlow 2s ease-in-out infinite alternate;
}

.loader-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
  letter-spacing: 1px;
}

/* 进度条 */
.progress-container {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 2px;
  width: 0%;
  animation: progressLoad 3s ease-out forwards;
}

/* 加载状态文字 */
.loading-status {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  min-height: 16px;
  animation: statusFade 0.5s ease-in-out;
}

/* 动画关键帧定义 */

/* 背景粒子闪烁动画 */
@keyframes sparkle {
  0%, 100% {
    opacity: 0.8;
    transform: translateY(0px);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px);
  }
}

/* 主图标脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
  }
}

/* 书本翻页动画 */
@keyframes bookFlip {
  0%, 20% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(-180deg);
  }
  80%, 100% {
    transform: rotateY(0deg);
  }
}

/* 图表柱状图生长动画 */
@keyframes chartGrow {
  0% {
    transform: scaleY(0.3);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1.2);
    opacity: 1;
  }
  100% {
    transform: scaleY(1);
    opacity: 0.8;
  }
}

/* 雷达扫描旋转动画 */
@keyframes radarScan {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.8;
  }
}

/* 雷达扫描线动画 */
@keyframes radarSweep {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
    opacity: 0.3;
  }
}

/* 标题发光动画 */
@keyframes titleGlow {
  0% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  100% {
    text-shadow:
      0 2px 4px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 255, 255, 0.5);
  }
}

/* 进度条加载动画 */
@keyframes progressLoad {
  0% {
    width: 0%;
  }
  25% {
    width: 30%;
  }
  50% {
    width: 60%;
  }
  75% {
    width: 85%;
  }
  100% {
    width: 100%;
  }
}

/* 状态文字淡入动画 */
@keyframes statusFade {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loader-icon {
    width: 100px;
    height: 100px;
    margin-bottom: 25px;
  }

  .book-icon {
    width: 40px;
    height: 32px;
  }

  .book-cover {
    width: 40px;
    height: 32px;
  }

  .book-pages {
    width: 36px;
    height: 28px;
  }

  .loader-title {
    font-size: 20px;
  }

  .progress-container {
    width: 250px;
  }

  .chart-elements {
    width: 25px;
    height: 25px;
    top: -15px;
    right: -15px;
  }

  .radar-scanner {
    width: 20px;
    height: 20px;
    top: -25px;
    left: -25px;
  }
}

@media (max-width: 480px) {
  .loader-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }

  .book-icon {
    width: 30px;
    height: 24px;
  }

  .book-cover {
    width: 30px;
    height: 24px;
  }

  .book-pages {
    width: 26px;
    height: 20px;
  }

  .loader-title {
    font-size: 18px;
  }

  .loader-subtitle {
    font-size: 12px;
  }

  .progress-container {
    width: 200px;
  }
}
