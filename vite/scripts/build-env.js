import { execSync } from 'node:child_process';
import { readFileSync, existsSync } from 'node:fs';
import { join } from 'node:path';
import process from 'node:process';
import { createInterface } from 'node:readline';
import { fileURLToPath } from 'node:url';
import boxen from 'boxen';
import gradient from 'gradient-string';

/**
 * 图标定义常量
 * @type {object}
 */
const ICONS = {
  APP: '🚀',
  ENV: '🌍',
  BUILD: '🔨',
  SUCCESS: '✅',
  ERROR: '❌',
  INFO: '📢',
  QUESTION: '❓',
  WARNING: '⚠️',
  EXIT: '🚪',
  STAGE: '🧪',
  PRODUCTION: '🏭',
  DEBUG: '🔧',
};

/**
 * 环境配置
 * @type {Array<{name: string, value: string, description: string, icon: string}>}
 */
const environments = [
  { name: '测试环境', value: 'stage', description: '用于提交测试服务器测试', icon: ICONS.STAGE },
  {
    name: '生产环境',
    value: 'production',
    description: '用于发布正式环境',
    icon: ICONS.PRODUCTION,
  },
];

/**
 * 增强的键盘监听器类 - 专门用于构建环境选择
 */
class BuildEnvKeyboardListener {
  constructor(options = {}) {
    this.isActive = false;
    this.originalRawMode = null;
    this.logger = options.logger || console;
    this.exitCallback = options.exitCallback || (() => {});
    this.boundHandler = null;
  }

  /**
   * 检测退出键组合
   * @param {Buffer} data - 键盘输入数据
   * @returns {boolean} 是否为退出键
   */
  isExitKey(data) {
    if (!data || data.length === 0) return false;

    const firstByte = data[0];

    // Ctrl+C (ETX - End of Text)
    if (firstByte === 3) return true;

    // Ctrl+D (EOT - End of Transmission)
    if (firstByte === 4) return true;

    return false;
  }

  /**
   * 获取退出键的描述
   * @param {Buffer} data - 键盘输入数据
   * @returns {string} 键的描述
   */
  getKeyDescription(data) {
    if (!data || data.length === 0) return '未知键';

    const firstByte = data[0];
    switch (firstByte) {
      case 3:
        return 'Ctrl+C';
      case 4:
        return 'Ctrl+D';
      default:
        return `键码${firstByte}`;
    }
  }

  /**
   * 安全地设置原始模式
   * @returns {boolean} 是否成功设置
   */
  setRawMode() {
    try {
      if (!process.stdin.isTTY) {
        this.logger.warn(`${ICONS.WARNING} 当前环境不支持TTY，键盘监听功能将被禁用`);
        return false;
      }

      // 保存当前状态
      this.originalRawMode = process.stdin.isRaw;

      process.stdin.setRawMode(true);
      process.stdin.resume();
      process.stdin.setEncoding('utf8');

      return true;
    } catch (error) {
      this.logger.error(`${ICONS.ERROR} 设置键盘原始模式失败:`, error.message);
      this.logger.info(`${ICONS.INFO} 键盘监听功能将被禁用，但程序将继续运行`);
      return false;
    }
  }

  /**
   * 安全地恢复终端状态
   */
  restoreTerminal() {
    try {
      if (process.stdin.isTTY && this.originalRawMode !== null) {
        process.stdin.setRawMode(this.originalRawMode);
        process.stdin.pause();
      }
    } catch (error) {
      this.logger.error(`${ICONS.ERROR} 恢复终端状态失败:`, error.message);
    }
  }

  /**
   * 处理键盘输入数据
   * @param {Buffer} data - 输入数据
   */
  handleKeyboardInput(data) {
    if (this.isExitKey(data)) {
      const keyDesc = this.getKeyDescription(data);
      this.logger.info(`${ICONS.DEBUG} 检测到退出键: ${keyDesc}`);

      try {
        this.exitCallback(keyDesc);
      } catch (error) {
        this.logger.error(`${ICONS.ERROR} 退出回调执行失败:`, error.message);
      }
    }
  }

  /**
   * 启动键盘监听
   * @returns {boolean} 是否成功启动
   */
  start() {
    if (this.isActive) {
      this.logger.warn(`${ICONS.WARNING} 键盘监听器已经在运行`);
      return true;
    }

    if (!this.setRawMode()) {
      return false;
    }

    this.boundHandler = this.handleKeyboardInput.bind(this);
    process.stdin.on('data', this.boundHandler);
    this.isActive = true;

    this.logger.info(`${ICONS.INFO} 键盘监听已启动 (Ctrl+C, Ctrl+D 可退出)`);
    return true;
  }

  /**
   * 停止键盘监听
   */
  stop() {
    if (!this.isActive) return;

    try {
      if (this.boundHandler) {
        process.stdin.removeListener('data', this.boundHandler);
        this.boundHandler = null;
      }

      this.restoreTerminal();
      this.isActive = false;

      this.logger.debug(`${ICONS.DEBUG} 键盘监听已停止`);
    } catch (error) {
      this.logger.error(`${ICONS.ERROR} 停止键盘监听失败:`, error.message);
    }
  }

  /**
   * 检查是否在TTY环境中
   * @returns {boolean}
   */
  static isTTYEnvironment() {
    return Boolean(process.stdin.isTTY && process.stdout.isTTY);
  }
}

/**
 * 资源管理器类 - 统一管理所有需要清理的资源
 */
class ResourceManager {
  constructor() {
    this.resources = new Set();
    this.isCleanedUp = false;
    this.logger = console;
  }

  /**
   * 添加需要清理的资源
   * @param {object} resource - 资源对象，必须有cleanup方法
   */
  addResource(resource) {
    if (resource && typeof resource.cleanup === 'function') {
      this.resources.add(resource);
    } else {
      this.logger.warn(`${ICONS.WARNING} 尝试添加无效资源，资源必须有cleanup方法`);
    }
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    if (this.isCleanedUp) return;

    this.logger.debug(`${ICONS.DEBUG} 开始清理资源...`);

    for (const resource of this.resources) {
      try {
        resource.cleanup();
      } catch (error) {
        this.logger.error(`${ICONS.ERROR} 清理资源失败:`, error.message);
      }
    }

    this.resources.clear();
    this.isCleanedUp = true;
    this.logger.debug(`${ICONS.DEBUG} 资源清理完成`);
  }

  /**
   * 设置进程退出时的自动清理
   */
  setupAutoCleanup() {
    const cleanup = () => this.cleanup();

    process.on('exit', cleanup);
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('SIGHUP', cleanup);
    process.on('uncaughtException', error => {
      this.logger.error(`${ICONS.ERROR} 未捕获的异常:`, error.message);
      cleanup();
      process.exit(1);
    });
    process.on('unhandledRejection', reason => {
      this.logger.error(`${ICONS.ERROR} 未处理的Promise拒绝:`, reason);
      cleanup();
      process.exit(1);
    });
  }
}

/**
 * Readline接口包装器 - 提供安全的readline操作
 */
class ReadlineWrapper {
  constructor() {
    this.interface = null;
    this.isActive = false;
    this.logger = console;
  }

  /**
   * 创建readline接口
   * @returns {boolean} 是否成功创建
   */
  create() {
    try {
      this.interface = createInterface({
        input: process.stdin,
        output: process.stdout,
      });
      this.isActive = true;
      return true;
    } catch (error) {
      this.logger.error(`${ICONS.ERROR} 创建readline接口失败:`, error.message);
      return false;
    }
  }

  /**
   * 提问并获取答案
   * @param {string} question - 问题
   * @returns {Promise<string>} 用户输入的答案
   */
  question(question) {
    return new Promise((resolve, reject) => {
      if (!this.interface) {
        reject(new Error('Readline接口未初始化'));
        return;
      }

      try {
        this.interface.question(question, answer => {
          resolve(answer);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 清理readline接口
   */
  cleanup() {
    if (this.interface && this.isActive) {
      try {
        this.interface.close();
        this.interface = null;
        this.isActive = false;
        this.logger.debug(`${ICONS.DEBUG} Readline接口已关闭`);
      } catch (error) {
        this.logger.error(`${ICONS.ERROR} 关闭readline接口失败:`, error.message);
      }
    }
  }
}

/**
 * 交互式环境选择和构建 - 优化版本
 * @returns {Promise<void>} Promise that resolves when build is complete
 */
export async function buildEnv() {
  // 创建资源管理器
  const resourceManager = new ResourceManager();
  resourceManager.setupAutoCleanup();

  let packageJson;
  let keyboardListener;
  let readlineWrapper;

  /**
   * 优雅退出处理
   * @param {string} reason - 退出原因
   */
  const gracefulExit = (reason = '用户取消') => {
    console.log(`\n${ICONS.EXIT} 已取消操作 (${reason})`);
    resourceManager.cleanup();
    process.exit(0);
  };

  /**
   * 初始化配置 - 简化版本
   */
  const initialize = () => {
    try {
      console.log(`${ICONS.INFO} 正在初始化配置...`);

      // 读取package.json
      const cwd = process.cwd();
      packageJson = JSON.parse(readFileSync(join(cwd, 'package.json'), 'utf8'));

      console.log(`${ICONS.SUCCESS} 配置初始化完成`);
    } catch (error) {
      console.error(`${ICONS.ERROR} 初始化失败: ${error.message}`);
      throw error;
    }
  };

  /**
   * 设置键盘监听
   */
  const setupKeyboardListener = () => {
    try {
      // 检查TTY环境
      if (!BuildEnvKeyboardListener.isTTYEnvironment()) {
        console.log(`${ICONS.INFO} 非TTY环境，使用信号处理机制`);
        return;
      }

      // 创建键盘监听器
      keyboardListener = new BuildEnvKeyboardListener({
        logger: console,
        exitCallback: keyDesc => gracefulExit(`${keyDesc}键`),
      });

      // 添加到资源管理器
      resourceManager.addResource(keyboardListener);

      // 启动监听
      keyboardListener.start();
    } catch (error) {
      console.error(`${ICONS.ERROR} 设置键盘监听失败:`, error.message);
      console.log(`${ICONS.INFO} 键盘监听功能已禁用，但程序将继续运行`);
    }
  };

  /**
   * 设置readline接口
   */
  const setupReadline = () => {
    try {
      readlineWrapper = new ReadlineWrapper();
      if (!readlineWrapper.create()) {
        throw new Error('无法创建readline接口');
      }

      // 添加到资源管理器
      resourceManager.addResource(readlineWrapper);
    } catch (error) {
      console.error(`${ICONS.ERROR} 设置readline接口失败:`, error.message);
      throw error;
    }
  };

  /**
   * 打印标题
   */
  const printTitle = () => {
    console.log('\n');
    console.log(
      boxen(
        gradient(['cyan', 'magenta']).multiline(
          `${ICONS.APP} 选择打包环境 - ${packageJson.name} ${packageJson.version}`,
        ),
        {
          padding: 1,
          borderColor: 'cyan',
          borderStyle: 'round',
        },
      ),
    );
    console.log('\n');
  };

  /**
   * 检测当前项目使用的包管理器
   * 基于运行时环境和进程信息进行动态检测
   * @returns {string} 包管理器名称 ('pnpm', 'yarn', 'npm', 'bun' 等)
   */
  const detectPackageManager = () => {
    console.log(`${ICONS.DEBUG} 开始检测包管理器...`);

    // 方法1: 检查 npm_execpath 环境变量
    const npmExecPath = process.env.npm_execpath;
    if (npmExecPath) {
      console.log(`${ICONS.DEBUG} npm_execpath: ${npmExecPath}`);

      if (npmExecPath.includes('pnpm')) {
        console.log(`${ICONS.SUCCESS} 通过 npm_execpath 检测到包管理器: pnpm`);
        return 'pnpm';
      }
      if (npmExecPath.includes('yarn')) {
        console.log(`${ICONS.SUCCESS} 通过 npm_execpath 检测到包管理器: yarn`);
        return 'yarn';
      }
      if (npmExecPath.includes('bun')) {
        console.log(`${ICONS.SUCCESS} 通过 npm_execpath 检测到包管理器: bun`);
        return 'bun';
      }
      if (npmExecPath.includes('npm')) {
        console.log(`${ICONS.SUCCESS} 通过 npm_execpath 检测到包管理器: npm`);
        return 'npm';
      }
    }

    // 方法2: 检查 npm_config_user_agent 环境变量
    const userAgent = process.env.npm_config_user_agent;
    if (userAgent) {
      console.log(`${ICONS.DEBUG} npm_config_user_agent: ${userAgent}`);

      if (userAgent.startsWith('pnpm/')) {
        console.log(`${ICONS.SUCCESS} 通过 user_agent 检测到包管理器: pnpm`);
        return 'pnpm';
      }
      if (userAgent.startsWith('yarn/')) {
        console.log(`${ICONS.SUCCESS} 通过 user_agent 检测到包管理器: yarn`);
        return 'yarn';
      }
      if (userAgent.startsWith('bun/')) {
        console.log(`${ICONS.SUCCESS} 通过 user_agent 检测到包管理器: bun`);
        return 'bun';
      }
      if (userAgent.startsWith('npm/')) {
        console.log(`${ICONS.SUCCESS} 通过 user_agent 检测到包管理器: npm`);
        return 'npm';
      }
    }

    // 方法3: 检查其他相关环境变量
    const packageManagerEnvVars = {
      PNPM_HOME: 'pnpm',
      YARN_WRAP_OUTPUT: 'yarn',
      BUN_INSTALL: 'bun',
    };

    for (const [envVar, manager] of Object.entries(packageManagerEnvVars)) {
      if (process.env[envVar]) {
        console.log(`${ICONS.SUCCESS} 通过环境变量 ${envVar} 检测到包管理器: ${manager}`);
        return manager;
      }
    }

    // 方法4: 检查进程标题和命令行参数
    const processTitle = process.title;
    const argv0 = process.argv0;

    console.log(`${ICONS.DEBUG} 进程标题: ${processTitle}`);
    console.log(`${ICONS.DEBUG} argv0: ${argv0}`);

    if (processTitle && (processTitle.includes('pnpm') || argv0.includes('pnpm'))) {
      console.log(`${ICONS.SUCCESS} 通过进程信息检测到包管理器: pnpm`);
      return 'pnpm';
    }
    if (processTitle && (processTitle.includes('yarn') || argv0.includes('yarn'))) {
      console.log(`${ICONS.SUCCESS} 通过进程信息检测到包管理器: yarn`);
      return 'yarn';
    }
    if (processTitle && (processTitle.includes('bun') || argv0.includes('bun'))) {
      console.log(`${ICONS.SUCCESS} 通过进程信息检测到包管理器: bun`);
      return 'bun';
    }

    // 方法5: 作为备用方案，检查锁文件
    console.log(`${ICONS.INFO} 运行时检测未成功，尝试检查锁文件...`);
    const cwd = process.cwd();

    const lockFiles = [
      { file: 'pnpm-lock.yaml', manager: 'pnpm' },
      { file: 'yarn.lock', manager: 'yarn' },
      { file: 'bun.lockb', manager: 'bun' },
      { file: 'package-lock.json', manager: 'npm' },
    ];

    for (const { file, manager } of lockFiles) {
      if (existsSync(join(cwd, file))) {
        console.log(`${ICONS.INFO} 通过锁文件 ${file} 检测到包管理器: ${manager}`);
        return manager;
      }
    }

    // 方法6: 尝试检查全局安装的包管理器
    console.log(`${ICONS.INFO} 尝试检查全局可用的包管理器...`);
    const managers = ['pnpm', 'yarn', 'bun', 'npm'];

    for (const manager of managers) {
      try {
        execSync(`${manager} --version`, { stdio: 'pipe' });
        console.log(`${ICONS.INFO} 检测到全局可用的包管理器: ${manager}`);
        // 如果是第一个找到的非npm包管理器，优先使用
        if (manager !== 'npm') {
          return manager;
        }
      } catch {
        // 忽略错误，继续检查下一个
      }
    }

    // 默认使用 npm
    console.log(`${ICONS.WARNING} 无法检测到明确的包管理器，使用默认值: npm`);
    return 'npm';
  };

  /**
   * 获取包管理器的显示信息
   * @param {string} manager - 包管理器名称
   * @returns {object} 包含图标和描述的对象
   */
  const getPackageManagerInfo = manager => {
    const managerInfo = {
      pnpm: { icon: '📦', description: 'pnpm (高性能包管理器)' },
      yarn: { icon: '🧶', description: 'Yarn (快速包管理器)' },
      bun: { icon: '🥟', description: 'Bun (超快运行时)' },
      npm: { icon: '📋', description: 'npm (默认包管理器)' },
    };

    return managerInfo[manager] || managerInfo.npm;
  };

  /**
   * 获取构建命令
   * @param {string} env - 环境名称
   * @returns {string} 构建命令
   */
  const getBuildCommand = env => {
    const packageManager = detectPackageManager();
    const managerInfo = getPackageManagerInfo(packageManager);

    console.log(`${ICONS.INFO} 使用包管理器: ${managerInfo.icon} ${managerInfo.description}`);

    // 根据不同的包管理器生成相应的命令
    switch (packageManager) {
      case 'pnpm':
        return `pnpm run build:${env}`;
      case 'yarn':
        return `yarn run build:${env}`;
      case 'bun':
        return `bun run build:${env}`;
      case 'npm':
      default:
        return `npm run build:${env}`;
    }
  };

  /**
   * 执行构建
   * @param {object} selectedEnv - 选择的环境
   */
  const executeBuild = async selectedEnv => {
    console.log(
      boxen(
        gradient(['cyan', 'magenta']).multiline(
          `${ICONS.BUILD} 正在构建 ${selectedEnv.name} (${selectedEnv.value})...`,
        ),
        {
          padding: 0.5,
          borderColor: 'cyan',
          borderStyle: 'round',
        },
      ),
    );

    try {
      execSync(getBuildCommand(selectedEnv.value), { stdio: 'inherit' });
      console.log(`\n${ICONS.SUCCESS} 构建完成！`);
    } catch (error) {
      console.error(`\n${ICONS.ERROR} 构建失败: ${error.message}`);
      throw error;
    }
  };

  /**
   * 处理用户输入
   * @param {string} answer - 用户输入
   * @returns {Promise<boolean>} 是否需要继续提示
   */
  const handleUserInput = async answer => {
    const index = Number.parseInt(answer.trim(), 10) - 1;

    if (index >= 0 && index < environments.length) {
      const selectedEnv = environments[index];
      await executeBuild(selectedEnv);
      return false; // 不需要继续提示
    } else {
      console.log(`\n${ICONS.WARNING} 无效的选项("${answer}")，请重新输入\n`);
      return true; // 需要继续提示
    }
  };

  /**
   * 交互式环境选择
   */
  const selectEnvironment = async () => {
    printTitle();
    console.log(`${ICONS.ENV} 请选择打包环境：\n`);

    environments.forEach((env, index) => {
      console.log(`${index + 1}. ${env.icon} ${env.name} (${env.value}) - ${env.description}`);
    });

    console.log('\n');
    console.log(`${ICONS.INFO} 提示: 按Ctrl+C或Ctrl+D可退出选择`);
    console.log('\n');

    // 循环提示用户输入
    while (true) {
      try {
        const answer = await readlineWrapper.question(
          `${ICONS.QUESTION} 请输入选项编号 [1-${environments.length}]: `,
        );
        const shouldContinue = await handleUserInput(answer);

        if (!shouldContinue) {
          break;
        }
      } catch (error) {
        console.error(`${ICONS.ERROR} 输入处理失败:`, error.message);
        break;
      }
    }
  };

  // 主执行流程 - 简化版本
  try {
    initialize(); // 现在是同步函数，无需await
    setupKeyboardListener();
    setupReadline();
    await selectEnvironment();
  } catch (error) {
    console.error(`${ICONS.ERROR} 程序执行失败:`, error.message);
    console.error(`${ICONS.DEBUG} 错误详情:`, error.stack);
  } finally {
    // 确保清理所有资源
    resourceManager.cleanup();
  }
}

// 如果直接运行此脚本，则执行buildEnv函数
// 检查是否直接运行此脚本
if (process.argv[1] && fileURLToPath(import.meta.url) === process.argv[1]) {
  buildEnv().catch(error => {
    console.error(`${ICONS.ERROR} 构建失败:`, error.message);
    process.exit(1);
  });
}
