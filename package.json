{"name": "saber", "version": "4.6.0", "type": "module", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build": "node ./vite/scripts/build-env.js", "serve": "vite preview --host"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@saber/nf-design-base-elp": "^1.3.0", "@saber/nf-form-design-elp": "^1.6.1", "@saber/nf-form-elp": "^1.6.3", "@smallwei/avue": "^3.7.1", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.4", "axios": "^1.11.0", "codemirror": "5.65.20", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "disable-devtool": "^0.3.9", "echarts": "^6.0.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.10.7", "highlight.js": "^11.11.1", "js-base64": "^3.7.8", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "nprogress": "^0.2.0", "sm-crypto": "^0.3.13", "unplugin-icons": "^22.2.0", "vue": "^3.5.18", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1", "vue-tippy": "^6.7.1", "vue3-clipboard": "^1.0.0", "vuex": "^4.1.0"}, "devDependencies": {"@iconify/json": "^2.2.373", "@iconify/vue": "^5.0.0", "@prettier/plugin-oxc": "^0.0.4", "@vitejs/plugin-vue": "^6.0.1", "@vue/compiler-sfc": "^3.5.18", "boxen": "^8.0.1", "gradient-string": "^3.0.0", "oxlint": "^1.11.2", "prettier": "^3.6.2", "sass": "^1.90.0", "terser": "^5.43.1", "unplugin-auto-import": "^20.0.0", "vite": "^7.1.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}